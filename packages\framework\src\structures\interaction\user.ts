import type { APIUserApplicationCommandInteraction } from "discord-api-types/v10";
import { BaseInteraction } from "./base.js";

export class UserContextInteraction extends BaseInteraction<APIUserApplicationCommandInteraction> {
	public get commandName() {
		return this.data.name;
	}

	public get targetId() {
		return this.payload.data.target_id;
	}

	public get target() {
		return this.data.resolved.users[this.targetId];
	}
}
