import type { APIApplicationCommandAutocompleteInteraction } from "discord-api-types/v10";
import { BaseInteraction, type CreateAutocompleteResponseOptions } from "./base.js";

export class AutoCompleteInteraction extends BaseInteraction<APIApplicationCommandAutocompleteInteraction> {
	public async respond(options: CreateAutocompleteResponseOptions & { with_response?: boolean }) {
		await this.api.interactions.createAutocompleteResponse(this.payload.id, this.payload.token, options);
	}
}
