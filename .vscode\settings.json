{"editor.codeActionsOnSave": {"source.fixAll": "always", "source.fixAll.eslint": "always", "source.organizeImports": "never"}, "editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.trimAutoWhitespace": false, "eslint.useESLintClass": true, "eslint.useFlatConfig": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "files.associations": {"tsconfig.eslint.json": "jsonc", "tsconfig.json": "jsonc"}, "files.eol": "\n", "files.insertFinalNewline": true, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/.hg/store/**": true, "**/.next/*/**": true, "**/.yarn/*/**": true, "**/dist/*/**": true, "**/node_modules/*/**": true}, "search.exclude": {"**/.next": true, "**/.yarn": true, "**/dist": true}, "npm.packageManager": "pnpm", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.tsdk": "node_modules/typescript/lib"}