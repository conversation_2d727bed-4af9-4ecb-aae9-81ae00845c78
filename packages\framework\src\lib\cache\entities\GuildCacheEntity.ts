import { createRecipe, DataType } from "bin-rw";
import { injectable } from "inversify";
import type { ICacheEntity } from "../ICacheEntity.js";

export interface CachedGuild {
	icon: string | null;
	id: string;
	name: string;
	owner_id: string;
}

@injectable()
export class GuildCacheEntity implements ICacheEntity<CachedGuild> {
	public readonly TTL = 120_000 * 2;

	public readonly recipe = createRecipe(
		{
			icon: DataType.String,
			id: DataType.U64,
			name: DataType.String,
			owner_id: DataType.U64,
		},
		200,
	);

	public makeKey(id: string): string {
		return `guild:${id}`;
	}
}
