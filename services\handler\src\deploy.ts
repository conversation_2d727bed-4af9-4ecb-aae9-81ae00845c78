import path from "node:path";
import process from "node:process";
import { fileURLToPath, pathToFileURL } from "node:url";
import { createREST } from "@fae/framework";
import { type APIApplicationCommand, Routes } from "discord-api-types/v10";
import { glob } from "tinyglobby";

const rest = createREST({ version: "10" }).setToken(process.env.DISCORD_TOKEN!);
const main = fileURLToPath(import.meta.url);
const directory = `${path.dirname(main) + path.sep}`.replaceAll("\\", "/");
const interactions = await glob(`${directory}interactions/**/*.js`);
const commands: APIApplicationCommand[] = [];

try {
	console.log("Start refreshing interaction (/) commands.");

	for (const interactionFile of interactions) {
		const { default: interaction } = await import(pathToFileURL(interactionFile).toString());
		commands.push(interaction);
	}

	await rest.put(Routes.applicationCommands(process.env.DISCORD_CLIENT_ID!), {
		body: [...commands],
	});
	console.log("Successfully reloaded interaction (/) commands.");
} catch (error) {
	console.error(error);
}
