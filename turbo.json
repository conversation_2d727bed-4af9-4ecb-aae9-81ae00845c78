{"$schema": "https://turbo.build/schema.json", "remoteCache": {"enabled": true}, "globalDependencies": ["tsconfig.json"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["src/**", "package.json", "tsconfig.json", "tsup.config.ts", ".swcrc"], "outputs": ["dist/**"], "outputLogs": "errors-only"}, "lint": {"dependsOn": ["^build"], "inputs": ["../../eslint.config.mjs", "../../biome.json", "../../tsconfig.eslint.json", "bin/**", "src/**", "package.json", "tsconfig.eslint.json", ".swcrc"], "outputs": [], "outputLogs": "errors-only"}, "format": {"dependsOn": ["^build"], "inputs": ["../../eslint.config.mjs", "../../biome.json", "../../tsconfig.eslint.json", "bin/**", "src/**", "package.json", "tsconfig.eslint.json", ".swcrc"], "outputs": [], "outputLogs": "errors-only"}, "dev": {"cache": false, "persistent": true}}}