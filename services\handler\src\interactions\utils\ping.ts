import {
	ApplicationCommandOptionType,
	ApplicationIntegrationType,
	InteractionContextType,
	type RESTPostAPIChatInputApplicationCommandsJSONBody,
} from "discord-api-types/v10";

export default {
	name: "ping",
	description: "show ping stats",
	options: [
		{
			name: "hide",
			description: "Hide the message (makes it ephemeral)",
			type: ApplicationCommandOptionType.Boolean,
			required: false,
		},
	],
	integration_types: [ApplicationIntegrationType.GuildInstall],
	contexts: [InteractionContextType.Guild],
} as const satisfies RESTPostAPIChatInputApplicationCommandsJSONBody;
