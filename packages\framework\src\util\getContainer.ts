import { <PERSON><PERSON><PERSON> } from "node:buffer";
import { request } from "node:http";
import { Env } from "../env.js";

export interface Container {
	Command: string;
	Created: number;
	HostConfig: { NetworkMode: string };
	Id: string;
	Image: string;
	ImageID: string;
	Labels: {
		[key: string]: string;
		"com.docker.compose.container-number": string;
	};
	Mounts: { Destination: string; Mode: string; Propagation: string; RW: boolean; Source: string; Type: string }[];
	Names: string[];
	NetworkSettings: {
		Networks: {
			[key: string]: {
				Aliases: null;
				DriverOpts: null;
				EndpointID: string;
				Gateway: string;
				GlobalIPv6Address: string;
				GlobalIPv6PrefixLen: number;
				IPAMConfig: null;
				IPAddress: string;
				IPPrefixLen: number;
				IPv6Gateway: string;
				Links: null;
				MacAddress: string;
				NetworkID: string;
			};
		};
	};
	Ports: { IP?: string; PrivatePort?: number; PublicPort?: number; Type?: string }[];
	State: string;
	Status: string;
}

export async function getContainer(containerId: string): Promise<Container> {
	const containers = (await new Promise((resolve, reject) => {
		const req = request(
			{
				socketPath: Env.DockSock,
				path: "http://localhost/containers/json",
				method: "GET",
			},
			(res) => {
				if (res.statusCode !== 200) {
					reject(new Error(`Failed to get containers: ${res.statusCode}`));
				}

				const buffers: Buffer[] = [];

				res.on("data", (chunk) => buffers.push(chunk));

				res.on("end", () => {
					try {
						resolve(JSON.parse(Buffer.concat(buffers).toString("utf8")));
					} catch (error) {
						reject(error);
					}
				});

				res.on("error", reject);
			},
		);

		req.end();
	})) as Container[];

	const container = containers.find((container) => container.Id.startsWith(containerId));

	if (!container) {
		throw new Error(`Container ${containerId} not found.`);
	}

	return container;
}
