{"files": {"includes": ["**", "!**/node_modules", "!**/dist", "!**/src/structures/clientPrototypes.ts", "!**/.source/", "!**/*.next", "!**/next-env.d.ts"]}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "main"}, "formatter": {"enabled": true, "formatWithErrors": true, "indentStyle": "tab", "indentWidth": 4, "lineEnding": "lf", "lineWidth": 120, "includes": ["**", "!**/node_modules", "!**/dist", "!**/src/structures/clientPrototypes.ts", "!**/.source/*", "!**/*.next", "!**/next-env.d.ts"]}, "assist": {"actions": {"source": {"organizeImports": "off"}}}, "linter": {"enabled": false, "includes": ["**", "!**/node_modules", "!**/node_modules/", "!**/.git/", "!**/dist/", "!**/src/structures/clientPrototypes.ts", "!**/src/structures/embedConstructor.ts", "!**/src/structures/rawPayloadClean.ts", "!**/src/utils/embedConstructorUtils.ts"], "rules": {"style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"semicolons": "always", "enabled": true}, "parser": {"unsafeParameterDecoratorsEnabled": true}}}