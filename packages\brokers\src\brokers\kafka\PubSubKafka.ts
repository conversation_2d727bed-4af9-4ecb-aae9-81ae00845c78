import type { IPubSubBroker } from "../Broker.js";
import { BaseKafkaBroker } from "./BaseKafka.js";

export class PubSubKafkaBroker<TEvents extends Record<string, any>>
	extends BaseKafkaBroker<TEvents>
	implements IPubSubBroker<TEvents>
{
	public async publish<Event extends keyof TEvents>(event: Event, data: TEvents[Event]): Promise<void> {
		await this.producer.send({
			topic: event as string,
			messages: [{ value: this.options.encode(data) }],
		});
	}

	protected async emitEvent(event: string, data: unknown) {
		const payload = {
			data,
			ack: async () => {},
		};

		// @ts-expect-error: Intended
		this.emit(event, payload);
	}
}
