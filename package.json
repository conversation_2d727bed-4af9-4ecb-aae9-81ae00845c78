{"name": "@fae/root", "version": "1.0.0", "type": "module", "workspaces": ["services/*", "packages/*"], "private": "true", "scripts": {"build": "turbo run build --concurrency=2", "build:affected": "turbo run build --filter=...[origin/main] --concurrency=2", "dev": "turbo run dev --concurrency=2", "dev:affected": "turbo run dev --filter=...[origin/main] --concurrency=2", "lint": "turbo run lint --concurrency=2", "lint:affected": "turbo run lint --filter=...[origin/main] --concurrency=2", "format": "turbo run format --concurrency=2", "format:affected": "turbo run format --filter=...[origin/main] --concurrency=2"}, "devDependencies": {"@biomejs/biome": "^2.2.2", "@types/lodash.merge": "^4.6.9", "eslint": "^9.34.0", "eslint-config-neon": "^0.2.7", "eslint-formatter-pretty": "^6.0.1", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-de-morgan": "^1.3.1", "lodash.merge": "^4.6.2", "tsup": "8.5.0", "turbo": "^2.5.6", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0"}, "packageManager": "pnpm@10.15.0"}