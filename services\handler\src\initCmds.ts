import path from "node:path";
import { fileURLToPath, pathToFileURL, URL } from "node:url";
import { container, dynamicImport, commandInfo, kCommands, type Command } from "@fae/framework";
import { glob } from "tinyglobby";

export function getDirectory() {
	const main = fileURLToPath(new URL("index.js", import.meta.url));
	return `${path.dirname(main) + path.sep}`.replaceAll("\\", "/");
}

export async function loadCommands() {
	const commands = container.get<Map<string, Command>>(kCommands);
	const commandFiles = await glob(`${getDirectory()}commands/**/*.js`, {
		ignore: ["**/sub/**"],
	});

	for (const filePath of commandFiles) {
		const cmdInfo = commandInfo(filePath);
		const dynamic = dynamicImport<new () => Command>(async () => import(pathToFileURL(filePath).toString()));
		const commandInstance = container.get<Command>((await dynamic()).default);

		if (commandInstance.options?.name) {
			for (const name of commandInstance.options.name) {
				commands.set(name, commandInstance);
			}
		} else {
			commands.set(cmdInfo!.name, commandInstance);
		}
	}
}
