name: CI Tests

on:
  push:
    branches: [main]
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
  DO_NOT_TRACK: 1
  TURBO_TELEMETRY_DISABLED: 1
  TURBO_CONCURRENCY: 4

jobs:
  quality:
    name: Build and Lint
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 24
          

      - name: Install dependencies
        uses: ./.github/pnpmCache

      - name: Determine Turbo filter
        id: filter
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "base=...[origin/${{ github.event.pull_request.base.ref }}]" >> $GITHUB_OUTPUT
          else
            echo "base=...[HEAD^1]" >> $GITHUB_OUTPUT
          fi

      - name: Build dependencies
        run: pnpm exec turbo run build --filter="${{ steps.filter.outputs.base }}" --concurrency=${{ env.TURBO_CONCURRENCY }}

      - name: Run linting
        run: pnpm exec turbo run lint --filter="${{ steps.filter.outputs.base }}" --concurrency=${{ env.TURBO_CONCURRENCY }} -- --format=compact
