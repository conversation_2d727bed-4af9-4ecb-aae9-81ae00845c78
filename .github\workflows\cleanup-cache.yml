name: Cleanup caches

on:
  pull_request:
    types:
      - closed
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read
  actions: write

jobs:
  cleanup:
    name: Cleanup caches
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5

      - name: Cleanup caches
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          REPO=${{ github.repository }}
          PR_NUMBER=${{ github.event.pull_request.number }}

          if [ "$PR_NUMBER" ]; then
            BRANCH_REF="refs/pull/${PR_NUMBER}/merge"
          else
            BRANCH_REF="${{ github.event.ref }}"
          fi
          
          echo "BRANCH_REF=${BRANCH_REF}"
          echo "Fetching list of cache keys for branch: $BRANCH_REF"

          cacheIds=$(gh cache list --limit 1000 --repo $REPO --ref $BRANCH_REF --json id --jq '.[].id')
          cacheKeys=$(gh cache list --limit 1000 --repo $REPO --ref $BRANCH_REF --json key --jq '.[].key')

          if [ -z "$cacheIds" ]; then
            echo "No cache keys found for this branch."
            exit 0
          fi

          echo "Identified cache keys:"
          echo "$cacheKeys"

          cacheKeyCount=$(echo "$cacheIds" | wc -l)
          echo "Total cache keys identified: $cacheKeyCount"

          echo "Deleting caches..."
          deletedCount=0
          set +e
          for cacheId in $cacheIds; do
            gh cache delete $cacheId --repo $REPO
            deletedCount=$((deletedCount + 1))
          done

          echo "Total caches deleted: $deletedCount"
          echo "Done"
