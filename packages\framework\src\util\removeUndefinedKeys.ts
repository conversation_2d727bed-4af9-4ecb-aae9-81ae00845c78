export function removeUndefinedKeys<T extends Record<string, unknown>>(obj: T): T {
	return Object.fromEntries(Object.entries(obj).filter(([, value]) => value !== undefined)) as T;
}

export function removeUndefinedDeep<T>(obj: T): T {
	if (Array.isArray(obj)) {
		return obj.map(removeUndefinedDeep) as T;
	}

	if (obj && typeof obj === "object") {
		return Object.fromEntries(
			Object.entries(obj).flatMap(([key, value]) =>
				value === undefined ? [] : [[key, removeUndefinedDeep(value)]],
			),
		) as T;
	}

	return obj;
}
