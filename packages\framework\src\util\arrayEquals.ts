/**
 * Checks whether two arrays contain the same elements (based on `Array#includes`).
 * Returns false if either array is undefined or their lengths differ.
 *
 * @param a - The first array
 * @param b - The second array
 * @returns Whether the arrays are considered equal
 */
export function arrayEquals<T>(a?: T[], b?: T[]): boolean {
	if (!a || !b || a.length !== b.length) {
		return false;
	}

	return a.every((item) => b.includes(item));
}
