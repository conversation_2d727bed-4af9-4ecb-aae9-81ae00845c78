import { GatewayDispatchEvents } from "@discordjs/core";
import { type Client, kClient, type Event } from "@fae/framework";
import { inject, injectable } from "inversify";

@injectable()
export default class implements Event {
	public readonly name = "Client ready handling";

	public readonly event = GatewayDispatchEvents.GuildCreate as const;

	public constructor(@inject(kClient) private readonly client: Client) {}

	public async execute(): Promise<void> {
		this.client.on(this.event, async ({ data: guild }) => {
			console.log(guild.id);
		});
	}
}
