name: Publish Docker Image

on:
  push:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  detect-changed:
    name: Detect Changed Apps
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    env:
      TURBO_SCM_BASE: ${{ github.event_name == 'pull_request' && github.event.pull_request.base.sha || github.event.before }}
    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Detect Changed Apps
        id: set-matrix
        run: |
          apps=$(pnpm dlx turbo ls --affected --output=json)

          matrix=$(echo "$apps" | jq -c '
            .packages.items
            | map({
                dir: (.path | gsub("\\\\"; "/")),
                name: (
                  .name
                  | sub("^@.*?\/"; "")
                  | gsub("[^a-zA-Z0-9]"; "-")
                  | ascii_downcase
                )
              })
          ')

          filtered=$(echo "$matrix" | jq -c '.[]' | while read -r entry; do
            dir=$(echo "$entry" | jq -r '.dir')
            if [ -f "$dir/Dockerfile" ]; then
              echo "$entry"
            else
              echo "⏭️ Skipping $dir (no Dockerfile)" >&2
            fi
          done | jq -cs '.')

          echo "matrix<<EOF" >> $GITHUB_OUTPUT
          echo "$filtered"   >> $GITHUB_OUTPUT
          echo "EOF"         >> $GITHUB_OUTPUT

  docker-publish:
    name: Docker Publish
    needs: detect-changed
    runs-on: ubuntu-latest
    if: needs.detect-changed.outputs.matrix != '[]' &&
        github.event.pull_request.draft == false &&
        github.event.pull_request.user.login != 'renovate[bot]'
    strategy:
      matrix:
        include: ${{ fromJson(needs.detect-changed.outputs.matrix) }}
      fail-fast: false
    env:
      IMAGE_NAME: fae-${{ matrix.name }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v5

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log into Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - name: Extract Docker Metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository_owner }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=latest,enable={{is_default_branch}}
            type=ref,event=pr,prefix=pr-
          
      - name: Build and Push Docker Image
        id: build-and-push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ matrix.dir }}/Dockerfile
          push: true
          build-args: |
            TAG=${{ steps.meta.outputs.version }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          secrets: |
            TURBO_TEAM=${{ secrets.TURBO_TEAM }}
            TURBO_TOKEN=${{ secrets.TURBO_TOKEN }}
