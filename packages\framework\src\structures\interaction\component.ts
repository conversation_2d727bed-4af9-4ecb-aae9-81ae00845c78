import type {
	APIMessageComponentInteraction,
	RESTPostAPIInteractionCallbackQuery,
	APIMessageComponentButtonInteraction as APITypesAPIMessageComponentButtonInteraction,
	APIMessageComponentSelectMenuInteraction as APITypesAPIMessageComponentSelectMenuInteraction,
	APIModalSubmitInteraction as APITypesAPIModalSubmitInteraction,
} from "discord-api-types/v10";
import { transformComponentInteractionRaw } from "../../Interaction.js";
import { BaseInteraction } from "./base.js";
import { InteractionCallbackReponse } from "./callback/response.js";

export class ComponentInteraction<
	T extends APIMessageComponentInteraction = APIMessageComponentInteraction,
> extends BaseInteraction<T> {
	public get customId() {
		return this.data.custom_id;
	}

	public get componentType() {
		return this.data.component_type;
	}

	public get components() {
		const interaction = this.payload as
			| APITypesAPIMessageComponentButtonInteraction
			| APITypesAPIMessageComponentSelectMenuInteraction
			| APITypesAPIModalSubmitInteraction;

		return transformComponentInteractionRaw(interaction);
	}

	public deferUpdate(
		options: RESTPostAPIInteractionCallbackQuery & { with_response: true },
	): Promise<InteractionCallbackReponse>;
	public deferUpdate(options?: RESTPostAPIInteractionCallbackQuery & { with_response?: false }): Promise<void>;
	public async deferUpdate(
		options: RESTPostAPIInteractionCallbackQuery & { with_response?: boolean } = {},
	): Promise<InteractionCallbackReponse | void> {
		if (this.deferred || this.replied) {
			throw new Error("You have already acknowledged this interaction.");
		}

		const response = await this.api.interactions.deferMessageUpdate(this.id, this.token, options);
		this.deferred = true;

		return options.with_response ? new InteractionCallbackReponse(response!, this.api) : undefined;
	}
}
