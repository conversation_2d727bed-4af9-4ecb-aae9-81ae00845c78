{"$schema": "https://json.schemastore.org/tsconfig.json", "compilerOptions": {"target": "ESNext", "moduleResolution": "nodenext", "module": "NodeNext", "resolveJsonModule": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "useUnknownInCatchVariables": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "importHelpers": false, "verbatimModuleSyntax": true, "inlineSources": true, "newLine": "lf", "noEmitHelpers": true, "preserveConstEnums": true, "removeComments": false, "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "useDefineForClassFields": true, "skipLibCheck": true}, "exclude": ["node_modules"]}