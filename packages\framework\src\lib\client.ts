import { setTimeout, clearTimeout } from "node:timers";
import { Collection } from "@discordjs/collection";
import type { API, APIGuildMember, MappedEvents, RequestGuildMembersResult, ToEventProps } from "@discordjs/core";
import type { PubSubRedisBroker } from "@fae/brokers";
import { DiscordSnowflake } from "@sapphire/snowflake";
import { AsyncEventEmitter } from "@vladfrangu/async_event_emitter";
import {
	type GatewayGuildMembersChunkPresence,
	GatewayDispatchEvents,
	GatewayOpcodes,
	type GatewayPresenceUpdateData,
	type GatewayRequestGuildMembersData,
	type GatewaySendPayload,
} from "discord-api-types/v10";
import { container, kBroker, kApi, kClient } from "../container/inversify.js";
import type { DiscordGatewayEventsMap } from "../types/Gateway.js";

function createTimeoutController(controller: AbortController, timeout: number) {
	let timer = setTimeout(() => controller.abort(), timeout);

	return {
		reset() {
			clearTimeout(timer);
			timer = setTimeout(() => controller.abort(), timeout);
		},
		clear() {
			clearTimeout(timer);
		},
	};
}

// type AnyInteraction =
// 	| AutoCompleteInteraction
// 	| BaseInteraction
// 	| ButtonInteraction
// 	| ChatInputInteraction
// 	| ComponentInteraction;

// type ModifiedMapEvents = {
// 	[K in keyof MappedEvents]: K extends typeof GatewayDispatchEvents.InteractionCreate
// 		? [ToEventProps<AnyInteraction>]
// 		: MappedEvents[K];
// };

export class Client extends AsyncEventEmitter<MappedEvents> {
	public readonly gateway: PubSubRedisBroker<DiscordGatewayEventsMap>;

	public readonly api: API;

	public constructor() {
		super();
		this.setMaxListeners(20);
		this.gateway = container.get<PubSubRedisBroker<DiscordGatewayEventsMap>>(kBroker);
		this.api = container.get<API>(kApi);
	}

	/**
	 * Requests guild members from the gateway and returns an async iterator
	 * that yields data from each guild members chunk event.
	 *
	 * @see {@link https://discord.com/developers/docs/topics/gateway-events#request-guild-members}
	 * @param options - The options for the request
	 * @param timeout - The timeout for waiting for each guild members chunk event (default 10 seconds)
	 * @example
	 * ```ts
	 * for await (const { members } of client.requestGuildMembersIterator({ guild_id: '1234567890', query: '', limit: 0 })) {
	 *   console.log(members);
	 * }
	 * ```
	 */
	public async *requestGuildMembersIterator(options: GatewayRequestGuildMembersData, timeout = 10_000) {
		const shardId = 0;
		const nonce = options.nonce ?? DiscordSnowflake.generate().toString();
		const controller = new AbortController();
		const timeoutCtrl = createTimeoutController(controller, timeout);

		await this.send(shardId, {
			op: GatewayOpcodes.RequestGuildMembers,
			// eslint-disable-next-line id-length
			d: { ...options, nonce },
		});

		const iterator = AsyncEventEmitter.on(this, GatewayDispatchEvents.GuildMembersChunk, {
			signal: controller.signal,
		});

		for await (const [{ data }] of iterator) {
			if (data.nonce !== nonce) {
				continue;
			}

			timeoutCtrl.clear();

			yield {
				members: data.members,
				nonce,
				notFound: data.not_found ?? null,
				presences: data.presences ?? null,
				chunkIndex: data.chunk_index,
				chunkCount: data.chunk_count,
			};

			if (data.chunk_index >= data.chunk_count - 1) {
				break;
			}

			timeoutCtrl.reset();
		}
	}

	/**
	 * Requests guild members from the gateway.
	 *
	 * @see {@link https://discord.com/developers/docs/topics/gateway-events#request-guild-members}
	 * @param options - The options for the request
	 * @param timeout - The timeout for waiting for each guild members chunk event
	 * @example
	 * Requesting specific members from a guild
	 * ```ts
	 * const { members } = await client.requestGuildMembers({ guild_id: '1234567890', user_ids: ['9876543210'] });
	 * ```
	 */
	public async requestGuildMembers(options: GatewayRequestGuildMembersData, timeout = 10_000) {
		const members: RequestGuildMembersResult["members"] = [];
		const notFound: RequestGuildMembersResult["notFound"] = [];
		const presences: RequestGuildMembersResult["presences"] = [];
		const nonce = options.nonce ?? DiscordSnowflake.generate().toString();

		for await (const data of this.requestGuildMembersIterator({ ...options, nonce }, timeout)) {
			members.push(...data.members);
			if (data.presences) {
				presences.push(...data.presences);
			}

			if (data.notFound) {
				notFound.push(...data.notFound);
			}
		}

		const collectedMembers = members.reduce(
			(col, member) => col.set(member.user.id, member),
			new Collection<string, APIGuildMember>(),
		);
		const collectedPresences = presences?.reduce(
			(col, member) => col.set(member.user.id, member),
			new Collection<string, GatewayGuildMembersChunkPresence>(),
		);
		return { collectedMembers, nonce, notFound, collectedPresences };
	}

	/**
	 * Updates the presence of the bot user
	 *
	 * @param shardId - The id of the shard to update the presence in
	 * @param options - The options for updating the presence
	 */
	public async updatePresence(shardId: number | null, options: GatewayPresenceUpdateData) {
		await this.send(shardId, {
			op: GatewayOpcodes.PresenceUpdate,
			// eslint-disable-next-line id-length
			d: options,
		});
	}

	public async send(shardId: number | null, payload: GatewaySendPayload): Promise<void> {
		await this.gateway.publish("send", { payload, shardId });
	}

	public async init(events: GatewayDispatchEvents[]) {
		for (const event of new Set(events)) {
			// @ts-expect-error: AEE nukes types here,
			this.gateway.on(event, async ({ data, ack }) => {
				const shardId = 0;
				this.emit(event, this.toEventProps(data, shardId));
				void ack();
			});
		}

		await this.gateway.subscribe(events);
		container.bind<this>(kClient).toConstantValue(this);
	}

	/**
	 * Increments max listeners by one, if they are not zero.
	 */
	public incrementMaxListeners() {
		const maxListeners = this.getMaxListeners();
		if (maxListeners !== 0) {
			this.setMaxListeners(maxListeners + 1);
		}
	}

	/**
	 * Decrements max listeners by one, if they are not zero.
	 */
	public decrementMaxListeners() {
		const maxListeners = this.getMaxListeners();
		if (maxListeners !== 0) {
			this.setMaxListeners(maxListeners - 1);
		}
	}

	private toEventProps<ObjectType>(obj: ObjectType, shardId: number): ToEventProps<ObjectType> {
		return {
			api: this.api,
			shardId,
			data: obj,
		};
	}
}
