import type { RESTAPIInteractionCallbackResourceObject } from "discord-api-types/v10";
import { Base } from "../../base.js";
import { Message } from "../../message.js";

export class InteractionCallbackResource extends Base<RESTAPIInteractionCallbackResourceObject> {
	public get type() {
		return this.payload.type;
	}

	public get activityInstance() {
		return this.payload.activity_instance;
	}

	public get message() {
		return new Message(this.payload.message!, this.api);
	}
}
