import { performance } from "node:perf_hooks";
import { Command, type InteractionParam, type ArgsParam } from "@fae/framework";
import { MessageFlags } from "discord-api-types/v10";
import type PingCommandType from "../../interactions/utils/ping.js";

export default class extends Command<typeof PingCommandType> {
	public override async chatInput(
		interaction: InteractionParam,
		args: ArgsParam<typeof PingCommandType>,
	): Promise<void> {
		const flags = args.hide ? MessageFlags.Ephemeral : undefined;
		await interaction.deferReply({ flags });

		const firstReply = await interaction.editReply({
			content: "calculating",
		});

		const times: number[] = [];
		let msgToMsgDelay: number | undefined;

		for (let idx = 0; idx < 4; idx++) {
			const start = performance.now();
			const msg = await interaction.followUp({
				content: `ping ${idx}`,
				flags: MessageFlags.Ephemeral,
			});

			times.push(performance.now() - start);
			msgToMsgDelay ??= msg.createdTimestamp - firstReply.createdTimestamp;
		}

		const highest = Math.round(Math.max(...times));
		const lowest = Math.round(Math.min(...times));
		const mean = Math.round(times.reduce((total, ms) => total + ms, 0) / times.length);

		const pings = [
			"# **Pong!**",
			`- Lowest: **${lowest}ms**`,
			`- Highest: **${highest}ms**`,
			`- Avg: **${mean}ms**`,
			`- Cmd→Reply: **${msgToMsgDelay!}ms**`,
		].join("\n");

		await interaction.editReply({
			content: pings,
		});
	}
}
