import { MessageBuilder } from "@discordjs/builders";
import { Collection } from "@discordjs/collection";
import type { ComponentType, CreateMessageOptions, EditMessageOptions } from "@discordjs/core";
import type { RequestData } from "@discordjs/rest";
import { DiscordSnowflake } from "@sapphire/snowflake";
import {
	type APIAttachment,
	type GatewayMessageCreateDispatchData,
	InteractionType,
	MessageReferenceType,
} from "discord-api-types/v10";
import type { CollectorOptions } from "../util/collector/collector.js";
import { type CollectedInteraction, InteractionCollector } from "../util/collector/interaction.js";
import { Base } from "./base.js";

export class Message extends Base<GatewayMessageCreateDispatchData> {
	public get activity() {
		return this.payload.activity;
	}

	public get attachments() {
		return this.payload.attachments?.reduce(
			(map, att) => map.set(att?.id, att),
			new Collection<string, APIAttachment>(),
		);
	}

	public get author() {
		return this.payload.author;
	}

	public get channelId() {
		return this.payload.channel_id;
	}

	public get content() {
		return this.payload.content;
	}

	public get createdAt() {
		return this.createdTimestamp && new Date(this.createdTimestamp).toISOString();
	}

	public get createdTimestamp() {
		return DiscordSnowflake.timestampFrom(this.messageId);
	}

	public get editedAt() {
		return this.editedTimestamp;
	}

	public get editedTimestamp() {
		return this.payload.edited_timestamp;
	}

	public get embeds() {
		return this.payload.embeds;
	}

	public get guildId() {
		return this.payload.guild_id;
	}

	public get inGuild() {
		return Boolean(this.guildId);
	}

	public get interactionMetadata() {
		return this.payload.interaction_metadata;
	}

	public get messageId() {
		return this.payload.id;
	}

	public get messageReference() {
		return this.payload.message_reference;
	}

	public get referencedMessage() {
		return this.payload.referenced_message;
	}

	public get nonce() {
		return this.payload.nonce;
	}

	public get pinned() {
		return this.payload.pinned;
	}

	public get reactions() {
		return this.payload.reactions;
	}

	public get type() {
		return this.payload.type;
	}

	public get webhookId() {
		return this.payload.webhook_id;
	}

	public async awaitMessageComponent<Interaction extends CollectedInteraction>(
		options: CollectorOptions<[Interaction]> & { componentType?: ComponentType } = {},
	): Promise<Interaction | undefined> {
		return new Promise((resolve, reject) => {
			const collector = new InteractionCollector({
				...options,
				channelId: this.channelId,
				guild: this.guildId,
				interactionType: InteractionType.MessageComponent,
				max: 1,
				message: this,
			});

			collector.once("end", (interactions, reason) => {
				const interaction = interactions.first();
				if (interaction) {
					resolve(interaction);
				} else {
					reject(new Error(`No interaction collected: ${reason}`));
				}
			});
		});
	}

	public async crosspost() {
		return this.api.channels.crosspostMessage(this.channelId, this.messageId);
	}

	public async delete(options: Pick<RequestData, "auth" | "reason" | "signal">) {
		await this.api.channels.deleteMessage(this.channelId, this.messageId, options);
		return this;
	}

	public async edit(options: Partial<EditMessageOptions>) {
		// @ts-expect-error: this is ok
		const payload = new MessageBuilder(options).toJSON();
		return this.api.channels.editMessage(this.channelId, this.messageId, payload);
	}

	public async forward(channelId: string) {
		return this.api.channels.createMessage(channelId, {
			message_reference: {
				channel_id: this.channelId,
				guild_id: this.guildId ?? undefined,
				message_id: this.messageId,
				type: MessageReferenceType.Forward,
			},
		});
	}

	public async pin() {
		await this.api.channels.pinMessage(this.channelId, this.messageId);
		return this;
	}

	public async removeAttachments() {
		return this.edit({ attachments: [] });
	}

	public async reply(options: CreateMessageOptions) {
		const payload = new MessageBuilder(options).toJSON();

		payload.nonce = DiscordSnowflake.generate().toString();

		payload.message_reference = {
			channel_id: this.channelId,
			fail_if_not_exists: false,
			guild_id: this.guildId ?? undefined,
			message_id: this.messageId,
			type: MessageReferenceType.Default,
			...payload.message_reference,
		};

		return this.api.channels.createMessage(this.channelId, payload);
	}

	public async unpin() {
		await this.api.channels.unpinMessage(this.channelId, this.messageId);
		return this;
	}
}
