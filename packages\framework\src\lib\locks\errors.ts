/**
 * Base class for lock-related errors.
 */
abstract class LockError extends Error {
	public constructor(message: string, name: string) {
		super(message);
		this.name = name;
	}
}

export class LockAcquisitionError extends LockError {
	/**
	 * Thrown when a lock could not be acquired
	 */
	public constructor(message: string) {
		super(message, "LockAcquisitionError");
	}
}

export class LockReleaseError extends LockError {
	/**
	 * Thrown when a lock could not be released
	 */
	public constructor(message: string) {
		super(message, "LockReleaseError");
	}
}

export class LockExtendError extends LockError {
	/**
	 * Thrown when a lock could not be extended
	 */
	public constructor(message: string) {
		super(message, "LockExtendError");
	}
}
