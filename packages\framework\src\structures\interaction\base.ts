import { Message<PERSON><PERSON>er, ModalBuilder } from "@discordjs/builders";
import { Collection } from "@discordjs/collection";
import {
	type APIEntitlement,
	type APIModalInteractionResponseCallbackData,
	type APICommandAutocompleteInteractionResponseCallbackData,
	type APIInteractionResponseCallbackData,
	type APIInteractionResponseDeferredChannelMessageWithSource,
	type RESTPostAPIInteractionCallbackQuery,
	InteractionType,
	type APIInteraction,
} from "@discordjs/core";
import { ChannelType } from "@discordjs/core";
import type { RawFile } from "@discordjs/rest";
import { Env } from "../../env.js";
import { Base } from "../base.js";
import { Message } from "../message.js";
import { InteractionCallbackReponse } from "./callback/response.js";

export type CreateAutocompleteResponseOptions = APICommandAutocompleteInteractionResponseCallbackData &
	RESTPostAPIInteractionCallbackQuery;

export type CreateInteractionDeferResponseOptions = APIInteractionResponseDeferredChannelMessageWithSource["data"] &
	RESTPostAPIInteractionCallbackQuery;

export type CreateInteractionFollowUpResponseOptions = APIInteractionResponseCallbackData & { files?: RawFile[] };
export interface CreateInteractionResponseOptions
	extends APIInteractionResponseCallbackData,
		RESTPostAPIInteractionCallbackQuery {
	files?: RawFile[];
}

export type CreateInteractionUpdateMessageResponseOptions = APIInteractionResponseCallbackData &
	RESTPostAPIInteractionCallbackQuery & { files?: RawFile[] };

export type CreateModalResponseOptions = APIModalInteractionResponseCallbackData & RESTPostAPIInteractionCallbackQuery;

export type EditInteractionResponseOptions = APIInteractionResponseCallbackData & { files?: RawFile[] };

export class BaseInteraction<T extends APIInteraction & Record<string, any> = APIInteraction> extends Base<T> {
	public deferred = false;

	public replied = false;

	public get entitlements() {
		return this.payload.entitlements?.reduce(
			(map, entitlement) => map.set(entitlement.id, entitlement),
			new Collection<string, APIEntitlement>(),
		);
	}

	public get autocomplete() {
		return this.payload.type === InteractionType.ApplicationCommandAutocomplete;
	}

	public get data() {
		return this.payload.data as T["data"];
	}

	public get channel() {
		return this.payload.channel;
	}

	public get channelId() {
		return this.channel?.id;
	}

	public get guild() {
		return this.payload.guild;
	}

	public get guildId() {
		return this.payload.guild_id;
	}

	public get guildLocale() {
		return this.payload.guild_locale;
	}

	public get guilds() {
		return this.api.guilds;
	}

	public get id() {
		return this.payload.id;
	}

	public get isRepliable() {
		return ![InteractionType.ApplicationCommandAutocomplete, InteractionType.Ping].includes(this.type);
	}

	public get locale() {
		return this.user.locale;
	}

	public get member() {
		return this.payload.member;
	}

	public get message() {
		return this.payload.message;
	}

	public get nsfw() {
		if (this.channel?.type === ChannelType.DM || this.channel?.type === ChannelType.GroupDM) {
			return false;
		}

		return Boolean(this.channel?.nsfw);
	}

	public get token() {
		return this.payload.token;
	}

	public get type() {
		return this.payload.type;
	}

	public get user() {
		return (this.payload.user ?? this.payload.member?.user)!;
	}

	public get userId() {
		return this.user.id;
	}

	public get username() {
		return this.user.username;
	}

	public async deleteReply(message = "@original") {
		return this.api.interactions.deleteReply(Env.DiscordClientId, this.token, message);
	}

	public deferReply(
		options: CreateInteractionDeferResponseOptions & { with_response: true },
	): Promise<InteractionCallbackReponse>;
	public deferReply(options?: CreateInteractionDeferResponseOptions & { with_response?: false }): Promise<void>;
	public async deferReply(
		options?: CreateInteractionDeferResponseOptions,
	): Promise<InteractionCallbackReponse | void> {
		if (this.deferred || this.replied) {
			throw new Error("You have already acknowledged this interaction.");
		}

		const response = await this.api.interactions.defer(this.id, this.token, options);
		this.deferred = true;

		return options?.with_response ? new InteractionCallbackReponse(response!, this.api) : undefined;
	}

	public async editReply(options: EditInteractionResponseOptions = {}, messageId?: string) {
		const response = await this.api.interactions.editReply(
			Env.DiscordClientId,
			this.token,
			options,
			messageId ?? "@original",
		);

		this.replied = true;

		return new Message(response, this.api);
	}

	public async followUp(options: CreateInteractionFollowUpResponseOptions) {
		const payload = new MessageBuilder(options).toJSON();

		const response = await this.api.interactions.followUp(Env.DiscordClientId, this.token, payload);
		this.replied = true;
		return new Message(response, this.api);
	}

	public reply(
		options: CreateInteractionResponseOptions & { with_response: true },
	): Promise<InteractionCallbackReponse>;
	public reply(options: CreateInteractionResponseOptions & { with_response?: false }): Promise<void>;
	public async reply(
		options: CreateInteractionResponseOptions & { with_response?: boolean },
	): Promise<InteractionCallbackReponse | void> {
		if (this.deferred || this.replied) {
			throw new Error("You have already acknowledged this interaction.");
		}

		const response = await this.api.interactions.reply(this.id, this.token, options);
		this.replied = true;

		return options.with_response ? new InteractionCallbackReponse(response!, this.api) : undefined;
	}

	public async showModal(modal: CreateModalResponseOptions & { with_response?: boolean }) {
		if (this.deferred || this.replied) {
			throw new Error("You have already acknowledged this interaction.");
		}

		const json = modal instanceof ModalBuilder ? modal.toJSON() : modal;

		await this.api.interactions.createModal(this.id, this.token, json);
		this.replied = true;
	}

	public updateMessage(
		options: CreateInteractionUpdateMessageResponseOptions & { with_response: true },
	): Promise<InteractionCallbackReponse>;
	public updateMessage(
		options: CreateInteractionUpdateMessageResponseOptions & { with_response?: false },
	): Promise<void>;
	public async updateMessage(
		options: CreateInteractionUpdateMessageResponseOptions & { with_response?: boolean },
	): Promise<InteractionCallbackReponse | void> {
		if (this.deferred || this.replied) {
			throw new Error("You have already acknowledged this interaction.");
		}

		const response = await this.api.interactions.updateMessage(this.id, this.token, options);
		this.replied = true;

		return options.with_response ? new InteractionCallbackReponse(response!, this.api) : undefined;
	}
}
