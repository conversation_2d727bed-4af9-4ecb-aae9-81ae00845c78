import { basename, extname } from "node:path";
import type { Awaitable } from "@discordjs/util";
import type { CommandPayload, CacheType } from "./types/ArgumentsOf.js";
import type { CommandInfo } from "./types/Command.js";
import type { Commands, InteractionParam, CommandMethod, ArgsParam, InteractionType } from "./types/Interaction.js";

export interface CommandOptions {
	description?: string;
	enabled?: boolean;
	name?: string[];
}

export abstract class Command<C extends CommandPayload = CommandPayload, T extends CacheType = "cached">
	implements Commands<C, T>
{
	public constructor(public options?: CommandOptions) {}

	public chatInput(
		_interaction: InteractionParam<CommandMethod.ChatInput, InteractionType.ApplicationCommand, T>,
		_args: ArgsParam<C, CommandMethod.ChatInput, InteractionType.ApplicationCommand, T>,
	): Awaitable<any> {
		const commandName = _interaction.commandName;
		console.log(
			{
				command: {
					name: commandName,
					type: _interaction.type,
				},
				userId: _interaction.user?.id,
			},
			`Received chat input for ${commandName}, but the command does not handle chat input`,
		);
	}

	public autocomplete(
		_interaction: InteractionParam<CommandMethod.Autocomplete, InteractionType.ApplicationCommand, T>,
		_args: ArgsParam<C, CommandMethod.Autocomplete, InteractionType.ApplicationCommand, T>,
	): Awaitable<any> {
		const commandName = _interaction.data.name;
		console.log(
			{
				command: {
					name: commandName,
					type: _interaction.type,
				},
				userId: _interaction.user?.id,
			},
			`Received autocomplete for ${commandName}, but the command does not handle autocomplete`,
		);
	}

	public messageContext(
		_interaction: InteractionParam<CommandMethod.MessageContext, InteractionType.ApplicationCommand, T>,
		_args: ArgsParam<C, CommandMethod.MessageContext, InteractionType.ApplicationCommand, T>,
	): Awaitable<any> {
		const commandName = _interaction.data?.name;
		console.log(
			{
				command: {
					name: commandName,
					type: _interaction.type,
				},
				userId: _interaction.user?.id,
			},
			`Received message context for ${commandName}, but the command does not handle message context`,
		);
	}

	public userContext(
		_interaction: InteractionParam<CommandMethod.UserContext, InteractionType.ApplicationCommand, T>,
		_args: ArgsParam<C, CommandMethod.UserContext, InteractionType.ApplicationCommand, T>,
	): Awaitable<any> {
		const commandName = _interaction.data?.name;
		console.log(
			{
				command: {
					name: commandName,
					type: _interaction.type,
				},
				userId: _interaction.user?.id,
			},
			`Received user context for ${commandName}, but the command does not handle user context`,
		);
	}

	public registerComponents?(): Promise<void> | void;
}

export function commandInfo(path: string): CommandInfo | null {
	if (extname(path) !== ".js") {
		return null;
	}

	return { name: basename(path, ".js") } as const;
}
