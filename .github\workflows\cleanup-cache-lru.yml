name: Cleanup Caches

on:
  schedule:
    - cron: "0 0 * * *"
  workflow_dispatch:

permissions:
  contents: read
  actions: write

jobs:
  cleanup:
    name: Cleanup All Caches
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5

      - name: Cleanup caches
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          REPO=${{ github.repository }}
          
          # 🐊
          gh cache delete --repo $REPO --all --succeed-on-no-caches

          echo "Cleanup completed."
