# syntax=docker.io/docker/dockerfile:1

# ================ #
#    Base Stage    #
# ================ #
FROM node:alpine AS base

ENV CI=true
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

WORKDIR /gateway

RUN corepack enable && corepack prepare pnpm@latest --activate

RUN apk update && apk add --no-cache gcompat dumb-init g++ make python3 

COPY . ./

# ================ #
#  Builder Stage   #
# ================ #
FROM base AS builder

ENV TURBO_TELEMETRY_DISABLED=1
ENV DO_NOT_TRACK=1

RUN pnpm install --frozen-lockfile

RUN --mount=type=secret,id=TURBO_TEAM,env=TURBO_TEAM \
    --mount=type=secret,id=TURBO_TOKEN,env=TURBO_TOKEN \
    pnpm exec turbo run build --filter='@fae/gateway...'

# ================ #
#  Pruned Stage    #
# ================ #
FROM builder AS pruned

RUN pnpm --filter='@fae/gateway' --prod deploy pruned 

# ================ #
#  Runner Stage   #
# ================ #
FROM node:alpine

WORKDIR /gateway

RUN addgroup --system --gid 996 docker \
 && addgroup --system --gid 1001 nodejs \
 && adduser --system --uid 1001 --ingroup nodejs gateway \
 && adduser gateway docker

USER gateway

COPY --from=base /usr/bin/dumb-init /usr/bin/dumb-init
COPY --from=pruned /gateway/pruned/dist /gateway/dist
COPY --from=pruned /gateway/pruned/node_modules /gateway/node_modules
COPY --from=pruned /gateway/pruned/package.json /gateway/package.json

ENTRYPOINT ["dumb-init", "--"]

CMD ["node", "--run", "start"]
