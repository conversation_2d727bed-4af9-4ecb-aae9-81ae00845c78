import { Container } from "inversify";

export const container = new Container({
	autobind: true,
	defaultScope: "Singleton",
});

export const kBroker = Symbol.for("Broker instance");
export const kRest = Symbol.for("Rest instance");
export const kApi = Symbol.for("Api instance");
export const kRedis = Symbol.for("Redis instance");
export const kLogger = Symbol.for("Logger instance");
export const kCommands = Symbol.for("Commands instance");
export const kClient = Symbol.for("Client instance");
