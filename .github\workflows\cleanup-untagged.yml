name: Daily Image Cleanup

on:
  schedule:
    - cron: '0 0 * * 0,1,3'
  workflow_dispatch:

jobs:
  ghcr-cleanup-image:
    runs-on: ubuntu-latest
    steps:
      - uses: dataaxiom/ghcr-cleanup-action@v1
        with:
          token: ${{ secrets.DOCKER_ACCESS_TOKEN }}
          package: fae
          delete-tags: "!latest"
          expand-packages: true
          use-regex: true
          delete-orphaned-images: true
          delete-untagged: true
          delete-ghost-images: true
          delete-partial-images: true
