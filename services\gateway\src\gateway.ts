import { randomBytes } from "node:crypto";
import { hostname } from "node:os";
import process from "node:process";
import type { PubSubRedisBroker } from "@fae/brokers";
import { createBroker, type DiscordGatewayEventsMap, createLogger, createREST } from "@fae/framework";
import { getContainer, getClusterShards } from "@fae/framework";
import { CompressionMethod, WebSocketManager, WebSocketShardEvents } from "@fae/ws";
import { GatewayIntentBits, Routes, type APIGatewayBotInfo } from "discord-api-types/v10";
import type pino from "pino";
import { retrieveSessionInfo, updateSessionInfo } from "./util/session.js";

const containerId = Number.parseInt(
	(await getContainer(hostname())).Labels?.["com.docker.compose.container-number"] ?? "",
	10,
);

const [start, end] = getClusterShards(containerId, 100, 1)!;

export class Gateway {
	readonly #broker: PubSubRedisBroker<DiscordGatewayEventsMap>;

	readonly #gateway: WebSocketManager;

	readonly #logger: pino.Logger;

	public constructor() {
		// use random group to fan out events
		this.#broker = createBroker({ group: randomBytes(20).toString("hex") });

		this.#logger = createLogger("gateway");

		this.#gateway = new WebSocketManager({
			compression: CompressionMethod.ZstdNative,
			shardIds: {
				start: start!,
				end: end!,
			},
			async fetchGatewayInformation() {
				const rest = createREST();
				return rest.get(Routes.gatewayBot()) as Promise<APIGatewayBotInfo>;
			},
			intents: GatewayIntentBits.MessageContent | GatewayIntentBits.Guilds | GatewayIntentBits.GuildMessages,
			retrieveSessionInfo,
			token: process.env.DISCORD_TOKEN!,
			updateSessionInfo,
		});

		this.#gateway
			.on(WebSocketShardEvents.Closed, (code, shardId) => this.#logger.warn({ code, shardId }, "Shard CLOSED"))
			.on(WebSocketShardEvents.Error, (error, shardId) => this.#logger.error({ error, shardId }, "Shard ERROR"))
			.on(WebSocketShardEvents.Debug, (message, shardId) => this.#logger.debug({ shardId }, message))
			.on(WebSocketShardEvents.Hello, (shardId) => this.#logger.info({ shardId }, "Shard HELLO"))
			.on(WebSocketShardEvents.Ready, (shardId) => this.#logger.info({ shardId }, "Shard READY"))
			.on(WebSocketShardEvents.Resumed, (shardId) => this.#logger.info({ shardId }, "Shard RESUMED"))
			.on(WebSocketShardEvents.Dispatch, async (data) => this.#broker.publish(data.t, data.d))
			.on(WebSocketShardEvents.HeartbeatComplete, ({ ackAt, heartbeatAt, latency }, shardId) =>
				this.#logger.debug({ shardId, ackAt, heartbeatAt, latency }, "Shard HEARTBEAT"),
			);

		// eslint-disable-next-line @typescript-eslint/unbound-method
		this.#broker.on("send", async ({ ack, data }) => {
			this.#logger.info({ data }, "Sending payload");

			const shards = await this.#gateway.getShardIds();
			const managedShards = new Set(shards);

			// If a specific shardId is provided but not managed by this instance, acknowledge and skip
			if (data.shardId && !managedShards.has(data.shardId)) {
				// Not significant enough to warrant a warning
				this.#logger.info({ data }, "Received payload for a shard this manager does not handle; skipping.");
				return ack();
			}

			const targets = data.shardId ? [data.shardId] : shards;

			await Promise.all(targets.map((id) => this.#gateway.send(id, data.payload)));

			await ack();
		});
	}

	public async connect(): Promise<void> {
		await this.#broker.subscribe(["send"]);
		await this.#gateway.connect();
	}
}
