import { InteractionOptionResolver } from "@fae/framework";
import { InteractionType, RESTJSONErrorCodes, type APIInteraction } from "discord-api-types/v10";

export function fetchCommandName(interaction: APIInteraction): string {
	const isCommand =
		interaction.type === InteractionType.ApplicationCommand ||
		interaction.type === InteractionType.ApplicationCommandAutocomplete;

	if (!isCommand) {
		return "";
	}

	const resolver = new InteractionOptionResolver(interaction);

	const parts = [interaction.data.name, resolver.getSubcommandGroup(false), resolver.getSubcommand(false)].filter(
		Boolean,
	);

	return parts.join(" ");
}

export function isIgnorable(_error: unknown): boolean {
	const error = _error as Error & { code: number };

	const ignorableErrorCodes = [
		RESTJSONErrorCodes.UnknownWebhook,
		RESTJSONErrorCodes.UnknownInteraction,
		RESTJSONErrorCodes.InteractionHasAlreadyBeenAcknowledged,
		RESTJSONErrorCodes.InvalidWebhookToken,
	];

	return ignorableErrorCodes.includes(Number(error.code));
}
