import { pathToFileURL } from "node:url";
import type { Event } from "@fae/framework";
import { container, dynamicImport } from "@fae/framework";
import { glob } from "tinyglobby";
import { getDirectory } from "./initCmds.js";

export async function loadEvents() {
	const events = await glob(`${getDirectory()}events/**/*.js`);

	for (const eventFile of events) {
		const dynamic = dynamicImport<new () => Event>(async () => import(pathToFileURL(eventFile).toString()));
		const eventInstance = container.get<Event>((await dynamic()).default);

		if (!eventInstance.disabled) {
			void eventInstance.execute();
		}
	}
}
