import common from "eslint-config-neon/common";
import node from "eslint-config-neon/node";
import prettier from "eslint-config-neon/prettier";
import typescript from "eslint-config-neon/typescript";
import { createTypeScriptImportResolver } from "eslint-import-resolver-typescript";
import merge from "lodash.merge";
import tseslint from "typescript-eslint";
import deMorgan from "eslint-plugin-de-morgan";

const commonFiles = "{js,mjs,cjs,ts,mts,cts,jsx,tsx}";

const commonRuleset = merge(...common, {
	files: [`**/*${commonFiles}`],
	rules: {
		"no-eq-null": 0,
		eqeqeq: [2, "always", { null: "ignore" }],
		"jsdoc/no-undefined-types": 0,
	},
});

const nodeRuleset = merge(...node, { files: [`**/*${commonFiles}`] });
const prettierRuleset = merge(...prettier, { files: [`**/*${commonFiles}`] });
const typeScriptRuleset = merge(...typescript, {
	files: [`**/*${commonFiles}`],
	languageOptions: {
		parserOptions: {
			warnOnUnsupportedTypeScriptVersion: false,
			allowAutomaticSingleRunInference: true,
			project: ["tsconfig.eslint.json", "services/*/tsconfig.eslint.json", "packages/*/tsconfig.eslint.json"],
		},
	},
	rules: {
		"@typescript-eslint/consistent-type-definitions": [2, "interface"],
		"@stylistic/js/array-element-newline": 0,
		"no-unreachable-loop": 2,
		"sonarjs/no-one-iteration-loop": 0,
		"@typescript-eslint/prefer-literal-enum-member": [
			2,
			{
				allowBitwiseExpressions: true,
			},
		],
		"@typescript-eslint/naming-convention": [
			2,
			{
				selector: "typeParameter",
				format: ["PascalCase"],
				custom: {
					regex: "^\\w{1,}",
					match: true,
				},
			},
		],
		"@typescript-eslint/prefer-nullish-coalescing": [
			2,
			{
				ignoreConditionalTests: true,
				ignoreMixedLogicalExpressions: true,
				ignorePrimitives: {
					bigint: false,
					boolean: true,
					number: false,
					string: false,
				},
			},
		],
	},
	settings: {
		"import-x/resolver-next": [
			createTypeScriptImportResolver({
				noWarnOnMultipleProjects: true,
				project: ["tsconfig.eslint.json", "services/*/tsconfig.eslint.json", "packages/*/tsconfig.eslint.json"],
			}),
		],
	},
});

export default tseslint.config(
	{
		ignores: ["**/node_modules/", ".git/", "**/dist/", "**/.next/", "**/.source/", "eslint.config.mjs"],
	},
	deMorgan.configs.recommended,
	commonRuleset,
	nodeRuleset,
	typeScriptRuleset,
	{
		files: ["**/*{ts,mts,cts,tsx}"],
		rules: { "jsdoc/no-undefined-types": 0 },
	},
	{
		files: ["**/*{js,mjs,cjs,jsx}"],
		rules: { "tsdoc/syntax": 0 },
	},
	prettierRuleset,
	{
		files: [`**/*${commonFiles}`],
		rules: {
			curly: [2, "all"],
		},
	},
);
