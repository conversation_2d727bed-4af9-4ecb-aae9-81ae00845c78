import type { RESTAPIInteractionCallbackObject } from "discord-api-types/v10";

export class InteractionCallback {
	public constructor(private readonly data: RESTAPIInteractionCallbackObject) {}

	public get id() {
		return this.data.id;
	}

	public get type() {
		return this.data.type;
	}

	public get activityInstanceId() {
		return this.data.activity_instance_id;
	}

	public get responseMessageEphemeral() {
		return this.data.response_message_ephemeral;
	}

	public get responseMessageId() {
		return this.data.response_message_id;
	}

	public get responseMessageLoading() {
		return this.data.response_message_loading;
	}
}
