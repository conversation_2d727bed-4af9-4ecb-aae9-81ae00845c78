import type {
	ApplicationCommandOptionType,
	ApplicationCommandType,
	APIGuildMember,
	APIPartialChannel,
	APIRole,
	Permissions,
	APIAttachment,
	ComponentType,
	ModalSubmitActionRowComponent,
	Snowflake,
	APIMessage,
	APIUser,
} from "discord-api-types/v10";

/**
 * Represents the possible cache states.
 */
export type CacheType = "cached" | "raw" | undefined;

/**
 * Resolves a type based on the given cache state `State`.
 *
 * @param State - The cache state, which can be 'cached', 'raw', or undefined.
 * @param CachedType - The type when State is 'cached'.
 * @param RawType - The type when State is 'raw'. Defaults to CachedType.
 * @param PresentType - The type when State is 'cached' | 'raw'. Defaults to CachedType | RawType.
 * @param Fallback - The fallback type when State is undefined. Defaults to PresentType | null.
 */
export type CacheTypeReducer<
	State extends CacheType,
	CachedType,
	RawType = CachedType,
	PresentType = CachedType | RawType,
	Fallback = PresentType | null,
> = State extends "cached"
	? CachedType
	: State extends "raw"
		? RawType
		: State extends "cached" | "raw"
			? PresentType
			: Fallback;

export type CommandPayload = Readonly<{
	name: string;
	type?: ApplicationCommandType | undefined;
}>;

export type ComponentPayload = Readonly<{
	componentType: ComponentType;
	components?: ModalSubmitActionRowComponent[] | undefined;
	customId: string;
	values?: string[] | undefined;
}>;

export const enum Runtime {
	Raw,
}

type Option = Readonly<
	{
		name: string;
		required?: boolean | undefined;
	} & (
		| {
				choices?: readonly Readonly<{ name: string; value: number }>[];
				type: ApplicationCommandOptionType.Integer | ApplicationCommandOptionType.Number;
		  }
		| {
				choices?: readonly Readonly<{ name: string; value: string }>[];
				type: ApplicationCommandOptionType.String;
		  }
		| {
				options?: readonly Option[];
				type: ApplicationCommandOptionType.Subcommand | ApplicationCommandOptionType.SubcommandGroup;
		  }
		| {
				type:
					| ApplicationCommandOptionType.Attachment
					| ApplicationCommandOptionType.Boolean
					| ApplicationCommandOptionType.Channel
					| ApplicationCommandOptionType.Mentionable
					| ApplicationCommandOptionType.Role
					| ApplicationCommandOptionType.User;
		  }
	)
>;

// Converts union to intersection
type UnionToIntersection<U> = (U extends unknown ? (k: U) => void : never) extends (k: infer I) => void ? I : never;

// Map option type to actual value type (raw only)
type TypeIdToType<T, O, C, P, R extends CacheType = "cached"> = T extends
	| ApplicationCommandOptionType.Subcommand
	| ApplicationCommandOptionType.SubcommandGroup
	? ArgumentsOfRaw<O, P, R>
	: T extends ApplicationCommandOptionType.String
		? C extends readonly { value: string }[]
			? C[number]["value"]
			: string
		: T extends ApplicationCommandOptionType.Integer | ApplicationCommandOptionType.Number
			? C extends readonly { value: number }[]
				? C[number]["value"]
				: number
			: T extends ApplicationCommandOptionType.Boolean
				? boolean
				: T extends ApplicationCommandOptionType.User
					? { member?: (APIGuildMember & { permissions: Permissions }) | undefined; user: APIUser }
					: T extends ApplicationCommandOptionType.Channel
						? APIPartialChannel & { permissions: Permissions }
						: T extends ApplicationCommandOptionType.Role
							? APIRole
							: T extends ApplicationCommandOptionType.Mentionable
								?
										| APIRole
										| {
												member?: (APIGuildMember & { permissions: Permissions }) | undefined;
												user: APIUser;
										  }
										| undefined
								: T extends ApplicationCommandOptionType.Attachment
									? APIAttachment
									: never;

// Convert single option to object property
type OptionToObject<O, P, Z extends CacheType = "cached"> = O extends {
	choices?: infer C | undefined;
	name: infer K;
	options?: infer O | undefined;
	required?: infer R | undefined;
	type: infer T;
}
	? K extends string
		? R extends true
			? { [k in K]: TypeIdToType<T, O, C, P, Z> }
			: T extends ApplicationCommandOptionType.Subcommand | ApplicationCommandOptionType.SubcommandGroup
				? { [k in K]: TypeIdToType<T, O, C, P, Z> }
				: { [k in K]?: TypeIdToType<T, O, C, P, Z> | undefined }
		: never
	: never;

// Compose arguments object from array of options
type ArgumentsOfRaw<O, P, T extends CacheType = "cached"> = O extends readonly any[]
	? UnionToIntersection<OptionToObject<O[number], P, T>>
	: never;

// Main exposed type for arguments from command or component payloads (raw only)
export type ArgumentsOf<
	C extends CommandPayload | ComponentPayload,
	P extends Runtime = Runtime.Raw,
	T extends CacheType = "cached",
> = C extends { options: readonly Option[] }
	? UnionToIntersection<OptionToObject<C["options"][number], P, T>>
	: C extends { type: ApplicationCommandType.Message }
		? { message: APIMessage }
		: C extends { type: ApplicationCommandType.User }
			? { user: { member?: (APIGuildMember & { permissions: Permissions }) | undefined; user: APIUser } }
			: C extends { componentType: ComponentType.Button }
				? never
				: C extends { componentType: ComponentType.ChannelSelect }
					? { channels: Map<Snowflake, APIPartialChannel & { permissions: Permissions }> }
					: C extends { componentType: ComponentType.MentionableSelect }
						? {
								members: Map<Snowflake, APIGuildMember & { permissions: Permissions }>;
								roles: Map<Snowflake, APIRole>;
								users: Map<Snowflake, APIUser>;
							}
						: C extends { componentType: ComponentType.RoleSelect }
							? { roles: Map<Snowflake, APIRole> }
							: C extends { componentType: ComponentType.StringSelect }
								? { values: string[] }
								: C extends { componentType: ComponentType.TextInput }
									? { value: string }
									: C extends { componentType: ComponentType.UserSelect }
										? {
												members: Map<Snowflake, APIGuildMember & { permissions: Permissions }>;
												users: Map<Snowflake, APIUser>;
											}
										: never;
