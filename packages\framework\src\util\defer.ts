import type { Awaitable } from "@discordjs/util";

/**
 * Executes a synchronous action when the current scope exits.
 * Inspired by <PERSON>'s `defer` statement, this function ensures that the provided action is
 * called automatically upon leaving the scope.
 *
 * @param action - A synchronous function to execute on scope exit.
 * @returns A Disposable object that triggers the action upon disposal.
 * @example
 * ```typescript
 * using _ = defer(() => logger.info("scope exited"));
 * // ... additional code
 * ```
 */
export function defer(action: () => unknown): Disposable {
	return {
		[Symbol.dispose]() {
			const result = action();
			if (result instanceof Promise) {
				throw new TypeError("Async actions are not allowed in defer. Use asyncDefer instead.");
			}
		},
	};
}

/**
 * Executes an asynchronous action when the current scope exits.
 * Inspired by <PERSON>'s `defer` statement, this function ensures that the provided asynchronous action is
 * called automatically upon leaving the scope.
 *
 * @param action - An asynchronous function to execute on scope exit.
 * @returns An AsyncDisposable object that triggers the asynchronous action upon disposal.
 * @example
 * ```typescript
 * await using _ = asyncDefer(async () => logger.info("scope exited"));
 * // ... additional code
 * ```
 */
export function asyncDefer(action: () => Awaitable<unknown>): AsyncDisposable {
	return {
		async [Symbol.asyncDispose]() {
			await action();
		},
	};
}
