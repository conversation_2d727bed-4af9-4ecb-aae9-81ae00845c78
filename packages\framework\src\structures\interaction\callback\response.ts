import type { RESTPostAPIInteractionCallbackWithResponseResult } from "discord-api-types/v10";
import { Base } from "../../base.js";
import { InteractionCallback } from "./callback.js";
import { InteractionCallbackResource } from "./resource.js";

export class InteractionCallbackReponse extends Base<RESTPostAPIInteractionCallbackWithResponseResult> {
	public get interaction() {
		return new InteractionCallback(this.payload.interaction);
	}

	public get resource() {
		return new InteractionCallbackResource(this.payload.resource!, this.api);
	}
}
