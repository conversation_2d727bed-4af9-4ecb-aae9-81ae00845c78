/* eslint-disable @typescript-eslint/no-use-before-define */
import { setTimeout, clearTimeout } from "node:timers";
import { Collection, type ReadonlyCollection } from "@discordjs/collection";
import { AsyncEventEmitter } from "@vladfrangu/async_event_emitter";

export interface CollectorEventTypes<Key, Value, Extras extends unknown[] = []> {
	collect: [Value, ...Extras];
	dispose: [Value, ...Extras];
	end: [collected: ReadonlyCollection<Key, Value>, reason: string];
	ignore: [Value, ...Extras];
}

export type CollectorFilter<Arguments extends unknown[]> = (...args: Arguments) => Promise<boolean> | boolean;

export interface CollectorOptions<FilterArguments extends unknown[]> {
	dispose?: boolean;
	filter?: CollectorFilter<FilterArguments>;
	idle?: number;
	time?: number;
}

export interface CollectorResetTimerOptions {
	idle?: number;
	time?: number;
}

export abstract class Collector<
	Key,
	Value,
	Extras extends unknown[] = [],
	EventTypes extends CollectorEventTypes<Key, Value, Extras> = CollectorEventTypes<Key, Value, Extras>,
> extends AsyncEventEmitter<EventTypes> {
	public collected: Collection<Key, Value>;

	public lastCollectedTimestamp: number | null;

	public ended: boolean;

	public filter: CollectorFilter<[Value, ...Extras]>;

	public options: CollectorOptions<[Value, ...Extras]>;

	private _timeout: NodeJS.Timeout | null;

	private _idletimeout: NodeJS.Timeout | null;

	private _endReason: string | null;

	protected constructor(options: CollectorOptions<[Value, ...Extras]> = {}) {
		super();
		this.filter = options.filter ?? (() => true);
		this.options = options;
		this.collected = new Collection<Key, Value>();
		this.ended = false;
		this.lastCollectedTimestamp = null;

		this._timeout = null;
		this._idletimeout = null;
		this._endReason = null;

		if (typeof this.filter !== "function") {
			throw new TypeError("Invalid type for options.filter: expected function");
		}

		this.handleCollect = this.handleCollect.bind(this);
		this.handleDispose = this.handleDispose.bind(this);

		if (options.time) {
			this._timeout = setTimeout(() => this.stop("time"), options.time).unref();
		}

		if (options.idle) {
			this._idletimeout = setTimeout(() => this.stop("idle"), options.idle).unref();
		}
	}

	public get lastCollectedAt(): Date | null {
		return this.lastCollectedTimestamp ? new Date(this.lastCollectedTimestamp) : null;
	}

	public get endReason(): string | null {
		return this._endReason;
	}

	public async handleCollect(...args: unknown[]): Promise<void> {
		const collectedId = await this.collect(...args);

		if (collectedId) {
			// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
			const filterResult = await this.filter(...args, this.collected);
			if (filterResult) {
				this.collected.set(collectedId, args[0] as Value);
				// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
				this.emit("collect", ...args);
				this.lastCollectedTimestamp = Date.now();

				if (this._idletimeout) {
					clearTimeout(this._idletimeout);
					this._idletimeout = setTimeout(() => this.stop("idle"), this.options.idle!).unref();
				}
			} else {
				// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
				this.emit("ignore", ...args);
			}
		}

		this.checkEnd();
	}

	public async handleDispose(...args: unknown[]): Promise<void> {
		if (!this.options.dispose) {
			return;
		}

		const key = this.dispose(...args);

		// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
		if (!key || !(await this.filter(...args, this.collected)) || !this.collected.has(key)) {
			return;
		}

		this.collected.delete(key);
		// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
		this.emit("dispose", ...args);
		this.checkEnd();
	}

	public get next(): Promise<Value> {
		return new Promise((resolve, reject) => {
			if (this.ended) {
				reject(this.collected);
				return;
			}

			const onCollect = (item: Value) => {
				cleanup();
				resolve(item);
			};

			const onEnd = () => {
				cleanup();
				reject(this.collected);
			};

			const cleanup = () => {
				// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
				this.removeListener("collect", onCollect);
				// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
				this.removeListener("end", onEnd);
			};

			// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
			this.on("collect", onCollect);
			// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
			this.on("end", onEnd);
		});
	}

	public stop(reason: string = "user"): void {
		if (this.ended) {
			return;
		}

		if (this._timeout) {
			clearTimeout(this._timeout);
			this._timeout = null;
		}

		if (this._idletimeout) {
			clearTimeout(this._idletimeout);
			this._idletimeout = null;
		}

		this._endReason = reason;
		this.ended = true;

		// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
		this.emit("end", this.collected, reason);
	}

	public resetTimer({ time, idle }: CollectorResetTimerOptions = {}): void {
		if (this._timeout) {
			clearTimeout(this._timeout);
			this._timeout = setTimeout(() => this.stop("time"), time ?? this.options.time).unref();
		}

		if (this._idletimeout) {
			clearTimeout(this._idletimeout);
			this._idletimeout = setTimeout(() => this.stop("idle"), idle ?? this.options.idle).unref();
		}
	}

	public checkEnd(): boolean {
		const reason = this.endReason;
		if (reason) {
			this.stop(reason);
		}

		return Boolean(reason);
	}

	public async *[Symbol.asyncIterator](): AsyncIterableIterator<[Value, ...Extras]> {
		const queue: [Value, ...Extras][] = [];
		const onCollect = (...item: [Value, ...Extras]) => queue.push(item);
		// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
		this.on("collect", onCollect);

		try {
			while (queue.length || !this.ended) {
				if (queue.length) {
					yield queue.shift()!;
				} else {
					await new Promise<void>((resolve) => {
						const tick = () => {
							// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
							this.removeListener("collect", tick);
							// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
							this.removeListener("end", tick);
							resolve();
						};

						// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
						this.on("collect", tick);
						// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
						this.on("end", tick);
					});
				}
			}
		} finally {
			// @ts-expect-error: Behold the field in which i grow my fucks, lay thine eyes upon it and you shall see that it is barren
			this.removeListener("collect", onCollect);
		}
	}

	/**
	 * Abstract method to implement: returns key or null if not collectible
	 */
	public abstract collect(...args: unknown[]): Key | Promise<Key | null> | null;

	/**
	 * Abstract method to implement: returns key or null if not disposable
	 */
	public abstract dispose(...args: unknown[]): Key | null;
}
