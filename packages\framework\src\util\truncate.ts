export function truncate(text: string, maxLength: number, splitChar = " "): string {
	if (text.length <= maxLength) {
		return text;
	}

	const words = text.split(splitChar);
	const ellipsis = "...";
	const limit = maxLength - ellipsis.length;
	let lengthSoFar = 0;
	const result: string[] = [];

	for (const word of words) {
		const nextLength = (result.length ? lengthSoFar + splitChar.length : lengthSoFar) + word.length;

		if (nextLength > limit) {
			break;
		}

		result.push(word);
		lengthSoFar = nextLength;
	}

	const truncated = result.join(splitChar).trim();

	return truncated.length === text.length || truncated.length === 0
		? text.slice(0, limit) + ellipsis
		: truncated + ellipsis;
}
