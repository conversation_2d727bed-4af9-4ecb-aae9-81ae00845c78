import { inject, injectable } from "inversify";
import type { <PERSON><PERSON> } from "ioredis";
import { kRedis } from "../../container/inversify.js";
import type { ICache } from "./ICache.js";
import type { ICacheEntity } from "./ICacheEntity.js";
import { RedisCache } from "./RedisCache.js";

/**
 * @remarks
 * Since this is a singleton factory, we "cache our caches" in a WeakMap to avoid additional computation on subsequent calls.
 */
@injectable()
export class CacheFactory {
	private readonly caches = new WeakMap<ICacheEntity<unknown>, ICache<unknown>>();

	public constructor(@inject(kRedis) private readonly redis: Redis) {}

	public build<ValueType>(entity: ICacheEntity<ValueType>): ICache<ValueType> {
		if (this.caches.has(entity)) {
			return this.caches.get(entity)! as ICache<ValueType>;
		}

		const cache = new RedisCache<ValueType>(this.redis, entity);
		this.caches.set(entity, cache);

		return cache;
	}
}
