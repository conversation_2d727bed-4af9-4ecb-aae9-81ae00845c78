{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "lockFileMaintenance": {"enabled": true, "dependencyDashboardApproval": true, "recreateWhen": "always", "rebaseWhen": "behind-base-branch", "branchTopic": "lock-file-maintenance", "commitMessageAction": "Lockfile maintenance", "schedule": "* 00 * * 0", "prBodyDefinitions": {"Change": "All locks refreshed"}}, "packageRules": [{"matchUpdateTypes": ["major"], "dependencyDashboardApproval": true}, {"semanticCommitType": "chore", "commitMessageAction": "Bump", "commitMessageExtra": "to {{#if isPinDigest}}{{{newDigestShort}}}{{else}}{{#if newValue}}{{{prettyNewVersion}}}{{else}}{{{newDigestShort}}}{{/if}}{{/if}}", "commitMessageTopic": "{{depName}}", "rangeStrategy": "bump", "recreateWhen": "always", "addLabels": ["dependencies"], "matchPackageNames": ["*"]}, {"matchDepTypes": ["dependencies", "require"], "semanticCommitType": "chore"}, {"matchDepTypes": ["devDependencies"], "semanticCommitType": "chore", "semanticCommitScope": "deps-dev"}, {"matchDepTypes": ["action"], "semanticCommitType": "ci", "semanticCommitScope": null, "addLabels": ["ci"], "automerge": true}, {"matchUpdateTypes": ["lockFileMaintenance"], "semanticCommitScope": null}, {"matchPackageNames": ["node"], "matchManagers": ["npm"], "matchDepTypes": ["engines"], "enabled": false}], "prConcurrentLimit": 10, "prHourlyLimit": 5, "schedule": "* 00 * * 0,6", "vulnerabilityAlerts": {"enabled": true, "schedule": ["at any time"], "minimumReleaseAge": null, "rangeStrategy": "update-lockfile", "commitMessageSuffix": "[SECURITY]", "branchTopic": "{{{datasource}}}-{{{depName}}}-vulnerability", "addLabels": ["security"], "prCreation": "immediate"}}