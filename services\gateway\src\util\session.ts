import { container, kRedis } from "@fae/framework";
import type { SessionInfo } from "@fae/ws";
import type { Redis } from "ioredis";

export async function updateSessionInfo(shardId: number, session: SessionInfo | null): Promise<void> {
	const redis = container.get<Redis>(kRedis);

	if (!session) {
		await clearSession(shardId);
		return;
	}

	await redis.hset(
		`shards:${shardId}`,
		"resumeURL",
		session.resumeURL,
		"sequence",
		session.sequence,
		"sessionId",
		session.sessionId,
		"shardCount",
		session.shardCount,
	);
}

export async function retrieveSessionInfo(shardId: number): Promise<SessionInfo | null> {
	const redis = container.get<Redis>(kRedis);

	const [resumeURL, sequence, sessionId, shardCount] = await redis.hmget(
		`shards:${shardId}`,
		"resumeURL",
		"sequence",
		"sessionId",
		"shardCount",
	);

	if (!sessionId || !sequence || !resumeURL || !shardCount) {
		return null;
	}

	return {
		shardId,
		resumeURL,
		sequence: Number(sequence),
		sessionId,
		shardCount: Number(shardCount),
	};
}

async function clearSession(shardId: number): Promise<void> {
	const redis = container.get<Redis>(kRedis);
	await redis.del(`shards:${shardId}`);
}
