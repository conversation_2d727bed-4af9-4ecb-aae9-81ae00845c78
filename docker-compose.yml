name: fae

services:
  redis:
    container_name: redis
    image: redis/redis-stack-server
    restart: unless-stopped
    shm_size: 256mb
    expose:
      - "6379"
    environment:
      REDIS_ARGS: "--requirepass admin --save 300 1 --appendonly yes"
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping"]
      interval: 10s
      timeout: 5s
    networks:
      - fae

  proxy:
    container_name: proxy
    image: ghcr.io/faebotto/fae-proxy:main
    restart: on-failure
    env_file:
      - .env
    networks:
      - fae

  gateway:
    container_name: gateway
    image: ghcr.io/faebotto/fae-gateway:latest
    restart: on-failure
    env_file:
      - .env
    networks:
      - fae

  handler:
    container_name: handler
    image: ghcr.io/faebotto/fae-handler:latest
    restart: on-failure
    env_file:
      - .env
    networks:
      - fae

volumes:
  redis-data:

networks:
  fae:
