import "reflect-metadata";
import { hostname } from "node:os";
import process from "node:process";
import { GatewayDispatchEvents } from "@discordjs/core";
import {
	createLogger,
	createBroker,
	createAPI,
	createREST,
	createCommands,
	Client,
	getContainer,
} from "@fae/framework";
import { loadCommands } from "./initCmds.js";
import { loadEvents } from "./initEvents.js";

const container = await getContainer(hostname());
const containerId = Number.parseInt(container.Labels?.["com.docker.compose.container-number"], 10);

const [logger] = await Promise.all([
	createLogger(),
	createBroker({ group: "handler", name: `consumer-${containerId}` }),
	createAPI(createREST()),
	createCommands(),
]);

process.on("uncaughtExceptionMonitor", (err, origin) => {
	logger.fatal({ err, origin }, "Uncaught exception. Likely a hard crash");
});

const client = new Client();

await client.init([
	GatewayDispatchEvents.InteractionCreate,
	GatewayDispatchEvents.MessageCreate,
	GatewayDispatchEvents.MessageDelete,
	GatewayDispatchEvents.MessageDeleteBulk,
	GatewayDispatchEvents.ChannelDelete,
	GatewayDispatchEvents.ThreadDelete,
	GatewayDispatchEvents.GuildDelete,
]);

await Promise.all([loadCommands(), loadEvents()]);

console.log("Successfully started.");
