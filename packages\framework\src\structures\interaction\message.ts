import type { APIMessageApplicationCommandInteraction } from "discord-api-types/v10";
import { Message } from "../index.js";
import { BaseInteraction } from "./base.js";

export class MessageContextInteraction extends BaseInteraction<APIMessageApplicationCommandInteraction> {
	public get commandName() {
		return this.data.name;
	}

	public get targetId() {
		return this.payload.data.target_id;
	}

	public get target() {
		return new Message(this.data.resolved.messages[this.targetId]!, this.api);
	}
}
