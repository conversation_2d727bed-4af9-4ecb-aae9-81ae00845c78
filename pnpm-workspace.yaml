packages:
  - services/*
  - packages/*

autoInstallPeers: false

ignoredBuiltDependencies:
  - bin-rw
  - esbuild

injectWorkspacePackages: true

onlyBuiltDependencies:
  - '@swc/core'
  - unrs-resolver

overrides:
  '@angular-eslint/eslint-plugin': npm:@favware/skip-dependency@latest
  '@angular-eslint/eslint-plugin-template': npm:@favware/skip-dependency@latest
  '@angular-eslint/template-parser': npm:@favware/skip-dependency@latest
  '@discordjs/core': 3.0.0-dev.1754524906-698b92c2a
  astro-eslint-parser: npm:@favware/skip-dependency@latest
  eslint-mdx: npm:@favware/skip-dependency@latest
  eslint-plugin-astro: npm:@favware/skip-dependency@latest
  eslint-plugin-cypress: npm:@favware/skip-dependency@latest
  eslint-plugin-mdx: npm:@favware/skip-dependency@latest
  eslint-plugin-rxjs: npm:@favware/skip-dependency@latest
  eslint-plugin-rxjs-angular: npm:@favware/skip-dependency@latest
  eslint-plugin-svelte3: npm:@favware/skip-dependency@latest
  eslint-plugin-vue: npm:@favware/skip-dependency@latest

peerDependencyRules:
  allowAny:
    - '*'
  ignoreMissing:
    - '*'

publicHoistPattern:
  - '*eslint*'

syncInjectedDepsAfterScripts:
  - build
