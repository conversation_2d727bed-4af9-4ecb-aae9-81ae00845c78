-- Guild configurations table - stores bot settings per guild
CREATE TABLE IF NOT EXISTS guild_configs (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    guild_id text NOT NULL,
    uploader_role_id text NULL,
    upload_channels text[] DEFAULT '{}',
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT unique_guild_config UNIQUE (guild_id)
);

COMMENT ON COLUMN guild_configs.guild_id IS 'Guild (server) ID to which the bot is configured';
COMMENT ON COLUMN guild_configs.uploader_role_id IS 'Role ID that identifies users as uploaders';
COMMENT ON COLUMN guild_configs.upload_channels IS 'Array of channel IDs where uploads are tracked';

-- Upload counts table - stores the core upload counts per user per guild
CREATE TABLE IF NOT EXISTS upload_counts (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    guild_id text NOT NULL,
    user_id text NOT NULL,
    total_uploads integer NOT NULL DEFAULT 0,
    deleted_uploads integer NOT NULL DEFAULT 0,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT unique_upload_count UNIQUE (guild_id, user_id)
);

COMMENT ON COLUMN upload_counts.guild_id IS 'Guild (server) ID to which the upload counts are tracked';
COMMENT ON COLUMN upload_counts.user_id IS 'User ID of the uploader';
COMMENT ON COLUMN upload_counts.total_uploads IS 'Total number of attachments uploaded by this user';
COMMENT ON COLUMN upload_counts.deleted_uploads IS 'Total number of deleted attachments that were uploaded by this user';

-- Indexes
CREATE INDEX IF NOT EXISTS idx_upload_counts_guild_user ON upload_counts (guild_id, user_id);
