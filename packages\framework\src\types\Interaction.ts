import type { Awaitable } from "@discordjs/util";
import type {
	AutoCompleteInteraction,
	ButtonInteraction,
	ChatInputInteraction,
	ComponentInteraction,
	MessageContextInteraction,
	UserContextInteraction,
} from "../structures/interaction/index.js";
import type { ArgumentsOf, CacheType, CommandPayload, ComponentPayload, Runtime } from "./ArgumentsOf.js";

export interface ChatInput<C extends CommandPayload, T extends CacheType = "cached"> {
	chatInput(interaction: ChatInputInteraction, args: ArgumentsOf<C, Runtime.Raw, T>, locale: string): Awaitable<any>;
}

export interface Autocomplete<C extends CommandPayload, T extends CacheType = "cached"> {
	autocomplete(
		interaction: AutoCompleteInteraction,
		args: ArgumentsOf<C, Runtime.Raw, T>,
		locale: string,
	): Awaitable<any>;
}

export interface MessageContext<C extends CommandPayload, T extends CacheType = "cached"> {
	messageContext(
		interaction: MessageContextInteraction,
		args: ArgumentsOf<C, Runtime.Raw, T>,
		locale: string,
	): Awaitable<any>;
}

export interface UserContext<C extends CommandPayload, T extends CacheType = "cached"> {
	userContext(
		interaction: UserContextInteraction,
		args: ArgumentsOf<C, Runtime.Raw, T>,
		locale: string,
	): Awaitable<any>;
}

export interface Button<C extends ComponentPayload, T extends CacheType = "cached"> {
	button(interaction: ButtonInteraction, args: ArgumentsOf<C, Runtime.Raw, T>, locale: string): Awaitable<any>;
}

export interface SelectMenu<C extends ComponentPayload, T extends CacheType = "cached"> {
	selectMenu(interaction: ComponentInteraction, args: ArgumentsOf<C, Runtime.Raw, T>, locale: string): Awaitable<any>;
}

export interface ModalSubmit<C extends ComponentPayload, T extends CacheType = "cached"> {
	modalSubmit(
		interaction: ComponentInteraction,
		args: ArgumentsOf<C, Runtime.Raw, T>,
		locale: string,
	): Awaitable<any>;
}

export type Commands<C extends CommandPayload, T extends CacheType = "cached"> = Autocomplete<C, T> &
	ChatInput<C, T> &
	MessageContext<C, T> &
	UserContext<C, T> & {
		[key: string]: any;
	};

export type Components<C extends ComponentPayload, T extends CacheType = "cached"> = Button<C, T> &
	ModalSubmit<C, T> &
	SelectMenu<C, T> & {
		[key: string]: any;
	};

export enum InteractionType {
	ApplicationCommand = 0,
	Component = 1,
}

export enum CommandMethod {
	Autocomplete = "autocomplete",
	ChatInput = "chatInput",
	MessageContext = "messageContext",
	UserContext = "userContext",
}

export enum ComponentMethod {
	Button = "button",
	ModalSubmit = "modalSubmit",
	SelectMenu = "selectMenu",
}

type CommandMethodParameters<
	C extends CommandPayload = CommandPayload,
	T extends string = CommandMethod.ChatInput,
	Z extends CacheType = "cached",
> = Parameters<Commands<C, Z>[T]>;

type ComponentMethodParameters<
	C extends ComponentPayload = ComponentPayload,
	T extends string = ComponentMethod.Button,
	Z extends CacheType = "cached",
> = Parameters<Components<C, Z>[T]>;

export type InteractionParam<
	C extends CommandMethod | ComponentMethod = CommandMethod.ChatInput,
	T extends InteractionType = InteractionType.ApplicationCommand,
	Z extends CacheType = "cached",
> = T extends InteractionType.Component
	? ComponentMethodParameters<ComponentPayload, C, Z>[0]
	: CommandMethodParameters<CommandPayload, C, Z>[0];

export type ArgsParam<
	C extends CommandPayload | ComponentPayload,
	M extends CommandMethod | ComponentMethod = CommandMethod.ChatInput,
	T extends InteractionType = InteractionType.ApplicationCommand,
	Z extends CacheType = "cached",
> = T extends InteractionType.Component
	? C extends ComponentPayload
		? ComponentMethodParameters<C, M, Z>[1]
		: never
	: C extends CommandPayload
		? CommandMethodParameters<C, M, Z>[1]
		: never;

export type LocaleParam<
	C extends CommandMethod | ComponentMethod = CommandMethod.ChatInput,
	T extends InteractionType = InteractionType.ApplicationCommand,
	Z extends CacheType = "cached",
> = T extends InteractionType.Component
	? ComponentMethodParameters<ComponentPayload, C, Z>[2]
	: CommandMethodParameters<CommandPayload, C, Z>[2];
