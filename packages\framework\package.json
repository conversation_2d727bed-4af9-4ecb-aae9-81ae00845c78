{"name": "@fae/framework", "version": "0.1.4", "author": "later", "private": true, "scripts": {"build:clean": "del-cli dist", "build:check": "tsc --emitDeclarationOnly", "build:esm": "swc ./src --strip-leading-paths --out-dir ./dist", "build": "pnpm build:clean && pnpm build:check && pnpm build:esm", "lint": "biome check . && cross-env TIMING=1 eslint src --format=pretty", "format": "biome check . --write && cross-env TIMING=1 eslint src --fix --format=pretty", "prepack": "pnpm build"}, "type": "module", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "directories": {"lib": "src"}, "files": ["dist", "scripts"], "dependencies": {"@discordjs/builders": "2.0.0-dev.1755604885-b1d96e251", "@discordjs/collection": "^2.1.1", "@discordjs/core": "3.0.0-dev.1754524906-698b92c2a", "@discordjs/rest": "dev", "@discordjs/util": "^1.1.1", "@fae/brokers": "workspace:*", "@msgpack/msgpack": "3.1.2", "@sapphire/bitfield": "^1.2.4", "@sapphire/snowflake": "3.5.5", "@swc/helpers": "^0.5.17", "@vladfrangu/async_event_emitter": "^2.4.6", "bin-rw": "^0.1.1", "discord-api-types": "^0.38.22", "inversify": "7.9.1", "ioredis": "5.7.0", "pino": "9.9.0", "zod": "^4.1.5"}, "devDependencies": {"@swc/cli": "^0.7.8", "@swc/core": "^1.13.5", "@types/node": "24.3.0", "cross-env": "^10.0.0", "del-cli": "6.0.0", "eslint": "9.34.0", "eslint-config-neon": "0.2.7", "eslint-formatter-compact": "8.40.0", "eslint-formatter-pretty": "6.0.1", "type-fest": "^4.41.0", "typescript": "^5.9.2"}, "packageManager": "pnpm@10.15.0"}