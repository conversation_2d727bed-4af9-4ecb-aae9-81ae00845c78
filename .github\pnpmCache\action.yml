name: "pnpm install"
description: "Run pnpm install with cache enabled"
runs:
  using: "composite"
  steps:
    - name: Set up swap space
      if: runner.os == 'Linux'
      uses: pierotofy/set-swap-space@master
      with:
        swap-size-gb: 10

    - uses: pnpm/action-setup@v4
      name: Install pnpm
      with:
        run_install: false

    - name: Expose pnpm config(s) through "$GITHUB_OUTPUT"
      id: pnpm-config
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

    - name: Cache rotation keys
      id: cache-rotation
      shell: bash
      run: |
        echo "YEAR_MONTH=$(/bin/date -u "+%Y%m")" >> $GITHUB_OUTPUT

    - uses: actions/cache@v4
      name: Setup pnpm cache
      with:
        path: ${{ steps.pnpm-config.outputs.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-cache-${{ steps.cache-rotation.outputs.YEAR_MONTH }}-${{ hashFiles('**/pnpm-lock.yaml', 'pnpm-workspace.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-cache-${{ steps.cache-rotation.outputs.YEAR_MONTH }}-

    - name: Install dependencies
      shell: bash
      run: |
        pnpm install --frozen-lockfile --prefer-offline --loglevel error
      env:
        HUSKY: "0"
        LEFTHOOK: "0"
