import process from "node:process";
import { REST, type RESTOptions } from "@discordjs/rest";
import { Env } from "../env.js";
import { container, kRest } from "./inversify.js";

export function createREST(options: Partial<RESTOptions> = {}) {
	const rest = new REST({
		api: options.api ?? Env.Proxy,
		hashLifetime: 60 * 1_000,
		hashSweepInterval: 60 * 1_000,
		handlerSweepInterval: 60 * 1_000,
		timeout: 900_000,
		globalRequestsPerSecond: Number.POSITIVE_INFINITY,
		...options,
	}).setToken(process.env.DISCORD_TOKEN!);
	container.bind<REST>(kRest).toConstantValue(rest);
	rest.on("rateLimited", console.log);
	return rest;
}
