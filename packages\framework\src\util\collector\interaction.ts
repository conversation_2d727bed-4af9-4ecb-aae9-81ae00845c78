import { Collection } from "@discordjs/collection";
import {
	type APIUser,
	GatewayDispatchEvents,
	type APIMessageComponentInteraction,
	type ComponentType,
	type InteractionType,
} from "discord-api-types/v10";
import { container, kClient } from "../../container/inversify.js";
import type { Client } from "../../lib/client.js";
import type { ComponentInteraction, ButtonInteraction, Message } from "../../structures/index.js";
import { resolveInteractionClass } from "../class.js";
import { type CollectorOptions, Collector } from "./collector.js";

export type CollectedInteraction = ButtonInteraction | ComponentInteraction<APIMessageComponentInteraction>;

export interface InteractionCollectorOptions<Interaction extends CollectedInteraction>
	extends CollectorOptions<[Interaction]> {
	channelId?: string;
	componentType?: ComponentType;
	guild?: string;
	interactionType?: InteractionType;
	max?: number;
	maxComponents?: number;
	maxUsers?: number;
	message?: Message;
}

export class InteractionCollector<Interaction extends CollectedInteraction> extends Collector<string, Interaction> {
	public readonly messageId: string | null;

	public readonly gateway: Client;

	public readonly channelId: string | null;

	public readonly guildId: string | null;

	public readonly interactionType: InteractionType | null;

	public readonly componentType: ComponentType | null;

	public readonly users = new Collection<string, APIUser>();

	public total = 0;

	public override readonly options: InteractionCollectorOptions<Interaction>;

	public constructor(options: InteractionCollectorOptions<Interaction> = {}) {
		super(options);
		this.options = options;

		this.gateway = container.get(kClient);
		const msg = options.message;
		this.messageId = msg?.messageId ?? null;
		this.channelId = msg?.channelId ?? options.channelId ?? null;
		this.guildId = options.guild ?? msg?.guildId ?? null;
		this.interactionType = options.interactionType ?? null;
		this.componentType = options.componentType ?? null;

		this._listen();

		this.once("end", () => {
			this._cleanup();
			this.gateway.decrementMaxListeners();
		});

		this.on("collect", (interaction) => {
			this.total++;
			this.users.set(interaction.user!.id, interaction.user!);
		});
	}

	public override get endReason(): string | null {
		const { max, maxComponents, maxUsers } = this.options;
		if (max && this.total >= max) {
			return "limit";
		}

		if (maxComponents && this.collected.size >= maxComponents) {
			return "componentLimit";
		}

		if (maxUsers && this.users.size >= maxUsers) {
			return "userLimit";
		}

		return super.endReason;
	}

	public collect(interaction: Interaction): string | null {
		return this._match(interaction) ? interaction.id : null;
	}

	public dispose(interaction: Interaction): string | null {
		return this._match(interaction) ? interaction.id : null;
	}

	public empty(): void {
		this.total = 0;
		this.collected.clear();
		this.users.clear();
		this.checkEnd();
	}

	private _match(collectedInteraction: Interaction): boolean {
		return (
			(!this.interactionType || collectedInteraction.type === this.interactionType) &&
			(!this.componentType || collectedInteraction.componentType === this.componentType) &&
			(!this.messageId || collectedInteraction.message?.id === this.messageId) &&
			(!this.channelId || collectedInteraction.channelId === this.channelId) &&
			(!this.guildId || collectedInteraction.guildId === this.guildId)
		);
	}

	private _listen(): void {
		this.gateway.incrementMaxListeners();

		this.gateway.on(GatewayDispatchEvents.InteractionCreate, async ({ data: interaction, api }) => {
			const InteractionClass = resolveInteractionClass(interaction);
			// @ts-expect-error: this is ok
			const tInteraction = new InteractionClass(interaction, api);
			await this.handleCollect(tInteraction);
		});

		if (this.messageId) {
			this.gateway
				.on(GatewayDispatchEvents.MessageDelete, ({ data: message }) => this._handleMessageDelete(message.id))
				.on(GatewayDispatchEvents.MessageDeleteBulk, ({ data: message }) =>
					this._handleBulkDelete(new Set(message.ids)),
				);
		}

		if (this.channelId) {
			this.gateway
				.on(GatewayDispatchEvents.ChannelDelete, ({ data: channel }) => this._handleChannelDelete(channel.id))
				.on(GatewayDispatchEvents.ThreadDelete, ({ data: thread }) => this._handleThreadDelete(thread.id));
		}

		if (this.guildId) {
			this.gateway.on(GatewayDispatchEvents.GuildDelete, ({ data: guild }) => this._handleGuildDelete(guild.id));
		}
	}

	private _cleanup(): void {
		this.gateway
			.off(GatewayDispatchEvents.InteractionCreate, async ({ data: interaction, api }) => {
				const InteractionClass = resolveInteractionClass(interaction);
				// @ts-expect-error: this is ok
				const tInteraction = new InteractionClass(interaction, api);
				await this.handleCollect(tInteraction);
			})
			.off(GatewayDispatchEvents.MessageDelete, ({ data: message }) => this._handleMessageDelete(message.id))
			.off(GatewayDispatchEvents.MessageDeleteBulk, ({ data: messages }) =>
				this._handleBulkDelete(new Set(messages.ids)),
			)
			.off(GatewayDispatchEvents.ChannelDelete, ({ data: channel }) => this._handleChannelDelete(channel.id))
			.off(GatewayDispatchEvents.ThreadDelete, ({ data: thread }) => this._handleThreadDelete(thread.id))
			.off(GatewayDispatchEvents.GuildDelete, ({ data: guild }) => this._handleGuildDelete(guild.id));
	}

	private readonly _handleMessageDelete = (messageId: string) => {
		if (messageId === this.messageId) {
			this.stop("messageDelete");
		}
	};

	private readonly _handleBulkDelete = (msgs: Set<string>) => {
		if (msgs.has(this.messageId!)) {
			this.stop("messageDelete");
		}
	};

	private readonly _handleChannelDelete = (channelId: string) => {
		if (channelId === this.channelId) {
			this.stop("channelDelete");
		}
	};

	private readonly _handleThreadDelete = (threadId: string) => {
		if (threadId === this.channelId) {
			this.stop("threadDelete");
		}
	};

	private readonly _handleGuildDelete = (guildId: string) => {
		if (guildId === this.guildId) {
			this.stop("guildDelete");
		}
	};
}
