/**
 * Responsible for caching data in Redis.
 */
export interface ICache<ValueType> {
	delete(id: string): Promise<void>;
	get(id: string): Promise<ValueType | null>;
	getOld(id: string): Promise<ValueType | null>;
	has(id: string): Promise<boolean>;
	select<K extends keyof ValueType>(id: string, key: K): Promise<ValueType[K] | null>;
	set(id: string, value: ValueType): Promise<void>;
	update(id: string, updateFn: (value: ValueType) => ValueType): Promise<boolean>;
	updatePartial(id: string, partial: Partial<ValueType>): Promise<boolean>;
}
