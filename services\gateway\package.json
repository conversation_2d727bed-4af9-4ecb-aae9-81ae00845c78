{"name": "@fae/gateway", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"build:clean": "del-cli dist", "build:check": "tsc --noEmit", "build:esm": "swc ./src --strip-leading-paths --out-dir ./dist", "build": "pnpm build:clean && pnpm build:check && pnpm build:esm", "lint": "biome check . && cross-env TIMING=1 eslint src --format=pretty", "format": "biome check . --write && cross-env TIMING=1 eslint src --fix --format=pretty", "start": "node --enable-source-maps dist/index.js"}, "dependencies": {"@fae/brokers": "workspace:*", "@fae/framework": "workspace:*", "@fae/ws": "workspace:*", "@swc/helpers": "^0.5.17", "discord-api-types": "0.38.22", "ioredis": "5.7.0", "pino": "9.9.0"}, "devDependencies": {"@swc/cli": "^0.7.8", "@swc/core": "^1.13.5", "@types/node": "^24.3.0", "cross-env": "^10.0.0", "del-cli": "^6.0.0", "eslint": "9.34.0", "eslint-config-neon": "0.2.7", "eslint-formatter-compact": "8.40.0", "eslint-formatter-pretty": "6.0.1", "typescript": "5.9.2"}, "packageManager": "pnpm@10.15.0"}