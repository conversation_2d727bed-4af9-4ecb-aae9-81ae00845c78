import { BitField, enumToObject } from "@sapphire/bitfield";
import { PermissionFlagsBits } from "discord-api-types/v10";

export const permissions = new BitField(enumToObject(PermissionFlagsBits));

export const SNOWFLAKE_MIN_LENGTH = 17;

export const AUTOCOMPLETE_CHOICE_LIMIT = 25;
export const AUTOCOMPLETE_CHOICE_NAME_LENGTH_LIMIT = 100;

export const EMBED_TITLE_LIMIT = 256;
export const EMBED_DESCRIPTION_LIMIT = 4_096;
export const EMBED_FOOTER_TEXT_LIMIT = 2_048;
export const EMBED_AUTHOR_NAME_LIMIT = 256;
export const EMBED_FIELD_LIMIT = 25;
export const EMBED_FIELD_NAME_LIMIT = 256;
export const EMBED_FIELD_VALUE_LIMIT = 1_024;

export const InviteRegex = /(?:^|\b)discord(?:(?:app)?\.com\/invite|\.gg(?:\/invite)?)\/(?<code>[\w-]{2,255})(?:$|\b)/i;
export const SnowflakeRegex = /^(?<id>\d{17,20})$/;
export const WebhookRegex =
	/(?<url>^https:\/\/(?:(?:canary|ptb).)?discord(?:app)?.com\/api(?:\/v\d+)?\/webhooks\/(?<id>\d+)\/(?<token>[\w-]+)\/?$)/;
