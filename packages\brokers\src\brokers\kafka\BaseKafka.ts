import { AsyncEventEmitter } from "@vladfrangu/async_event_emitter";
import { type Consumer, type Producer } from "kafkajs";
import { Kafka } from "kafkajs";
import type { IBaseBroker, ToEventMap, BaseBrokerOptions } from "../Broker.js";
import { DefaultBrokerOptions } from "../Broker.js";

export interface KafkaBrokerOptions extends BaseBrokerOptions {
	brokers: string[];
	clientId: string;
	groupId: string;
}

export const DefaultKafkaBrokerOptions = {
	...DefaultBrokerOptions,
	clientId: "fae-broker",
	groupId: "fae-group",
} as const satisfies Required<Omit<KafkaBrokerOptions, "brokers">>;

export abstract class BaseKafkaBroker<
		TEvents extends Record<string, any[]>,
		TResponses extends Record<keyof TEvents, any> | undefined = undefined,
	>
	extends AsyncEventEmitter<ToEventMap<TEvents, TResponses>>
	implements IBaseBroker<TEvents>
{
	protected readonly options: Required<KafkaBrokerOptions>;

	protected readonly kafka: Kafka;

	protected readonly producer: Producer;

	protected readonly consumer: Consumer;

	protected readonly subscribedEvents = new Set<string>();

	protected listening = false;

	public constructor(options: KafkaBrokerOptions) {
		super();
		this.options = { ...DefaultKafkaBrokerOptions, ...options };
		this.kafka = new Kafka({ brokers: options.brokers, clientId: this.options.clientId });
		this.producer = this.kafka.producer();
		this.consumer = this.kafka.consumer({ groupId: this.options.groupId });

		// @ts-expect-error: Intended
		this.consumer.on("consumer.crash", ({ payload }) => this.emit("error", payload.error));
	}

	public async subscribe(events: (keyof TEvents)[]): Promise<void> {
		for (const event of events) {
			this.subscribedEvents.add(event as string);

			try {
				await this.consumer.subscribe({ topic: event as string, fromBeginning: false });
			} catch (error) {
				// @ts-expect-error: intended
				this.emit("error", error);
			}
		}

		if (!this.listening) {
			try {
				await Promise.all([this.consumer.connect(), this.producer.connect()]);
				void this.listen();
			} catch (error) {
				// @ts-expect-error: intended
				this.emit("error", error);
			}
		}
	}

	public async unsubscribe(events: (keyof TEvents)[]): Promise<void> {
		for (const event of events) {
			this.subscribedEvents.delete(event as string);
		}
	}

	protected async listen(): Promise<void> {
		this.listening = true;
		await this.consumer.run({
			eachMessage: async ({ topic, message }) => {
				await this.emitEvent(topic, this.options.decode(message.value!));
			},
		});
	}

	public async destroy() {
		try {
			await Promise.all([this.consumer.disconnect(), this.producer.disconnect()]);
		} catch (error) {
			// @ts-expect-error: intended
			this.emit("error", error);
		}
	}

	protected abstract emitEvent(event: string, data: unknown): Promise<void>;
}
