import { setTimeout } from "node:timers";
import { GatewayDispatchEvents } from "@discordjs/core";
import { type Client, kClient, type Event, Message } from "@fae/framework";
import { inject, injectable } from "inversify";

@injectable()
export default class implements Event {
	public readonly name = "tomfoolery";

	public readonly event = GatewayDispatchEvents.MessageCreate as const;

	private readonly pussyMessages = new Set<string>();

	public constructor(@inject(kClient) private readonly client: Client) {}

	public async execute(): Promise<void> {
		this.client.on(this.event, async ({ data: msg, api }) => {
			const message = new Message(msg, api);

			const content = message.content.toLowerCase().trim();

			if (/pussy/i.test(message.content)) {
				await this.handlePussy(message);
			} else if (content === "?ping") {
				await message.reply({ content: "ping pong!" });
			}
		});
	}

	private async handlePussy(message: Message) {
		const [replyMessage] = await Promise.all([
			message.reply({ content: "yo mama a hoe" }),
			message.delete({ reason: "offensive content" }),
		]);

		this.pussyMessages.add(replyMessage.id);

		setTimeout(async () => {
			const messagesToDelete = [...this.pussyMessages];
			if (messagesToDelete.length === 0) {
				return;
			}

			try {
				// eslint-disable-next-line @typescript-eslint/dot-notation
				await message["api"].channels.bulkDeleteMessages(message.channelId, messagesToDelete);
				this.pussyMessages.clear();
			} catch (error) {
				console.error("Failed to bulk delete messages:", error);
			}
		}, 60_000);
	}
}
