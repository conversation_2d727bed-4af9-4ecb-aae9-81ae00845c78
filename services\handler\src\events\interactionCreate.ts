import { ApplicationCommandType, GatewayDispatchEvents, InteractionType, MessageFlags } from "@discordjs/core";
import {
	AutoCompleteInteraction,
	BaseInteraction,
	ChatInputInteraction,
	Client,
	Command,
	kClient,
	kCommands,
	kLogger,
	transformApplicationInteractionRaw,
	type Event,
} from "@fae/framework";
import { inject, injectable } from "inversify";
import type { Logger } from "pino";
import { fetchCommandName } from "@/util/command.js";

@injectable()
export default class implements Event {
	public readonly name = "Interaction handling";

	public readonly event = GatewayDispatchEvents.InteractionCreate as const;

	public constructor(
		@inject(kCommands) private readonly commands: Map<string, Command>,
		@inject(kLogger) private readonly logger: Logger,
		@inject(kClient) private readonly client: Client,
	) {}

	public async execute(): Promise<void> {
		this.client.on(this.event, async ({ data: interaction, api }) => {
			if (
				![
					InteractionType.Ping,
					InteractionType.ApplicationCommand,
					InteractionType.ApplicationCommandAutocomplete,
				].includes(interaction.type)
			) {
				return;
			}

			const commandName = fetchCommandName(interaction);
			const command = this.commands.get(commandName);

			if (!command) {
				this.logger.debug(`No command found for name: ${commandName}`);
				return;
			}

			try {
				switch (interaction.type) {
					case InteractionType.Ping:
						this.logger.warn("🕵️‍♂️ Received Ping in command handler — likely a stray echo from the maze.");
						break;

					case InteractionType.ApplicationCommand: {
						try {
							// eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check, sonarjs/no-nested-switch
							switch (interaction.data.type) {
								case ApplicationCommandType.ChatInput: {
									// @ts-expect-error: types
									const chatInput = new ChatInputInteraction(interaction, api);
									await command.chatInput(
										chatInput,
										transformApplicationInteractionRaw(chatInput.data.options ?? []),
									);
									break;
								}
							}
						} catch (error_) {
							const error = error_ as Error;
							this.logger.error(error, error.message);

							const base = new BaseInteraction(interaction, api);

							try {
								if (base.autocomplete) {
									return;
								}

								if (!base.deferred && !base.replied) {
									this.logger.warn(
										{
											command: { name: base?.data?.name ?? "unknown", type: base.type },
											userId: base.user.id,
										},
										"Command interaction was not deferred before throwing",
									);
									await base.deferReply({ flags: MessageFlags.Ephemeral });
								}

								await base.editReply({ content: error.message, components: [] });
							} catch (subError_) {
								const subError = subError_ as Error;
								this.logger.error(subError, subError.message);
							}
						}

						break;
					}

					case InteractionType.ApplicationCommandAutocomplete: {
						const autocomplete = new AutoCompleteInteraction(interaction, api);
						await command.autocomplete(
							autocomplete,
							transformApplicationInteractionRaw(autocomplete.data.options ?? []),
						);
						break;
					}

					case InteractionType.MessageComponent:
					case InteractionType.ModalSubmit:
						// No-op for now
						break;
				}
			} catch (_outerError) {
				const outerError = _outerError as Error;
				this.logger.error(outerError, outerError.message);
			}
		});
	}
}
