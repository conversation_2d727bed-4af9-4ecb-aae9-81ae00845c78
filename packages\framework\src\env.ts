import process from "node:process";
import { z } from "zod/v4";
import { SnowflakeRegex, WebhookRegex } from "./constants.js";

export const envSchema = z.object({
	Devs: z
		.string()
		.transform((value) => value.split(","))
		.pipe(z.array(z.string().regex(SnowflakeRegex)))
		.transform((value) => new Set(value))
		.optional(),
	DiscordClientId: z.string().regex(SnowflakeRegex),
	DiscordToken: z.string(),
	DockSock: z.string().default("/var/run/docker.sock"),
	NodeEnv: z.enum(["dev", "prod"]).default("prod"),
	Postgres: z.string().optional(),
	Proxy: z.url().optional(),
	Redis: z.url().optional(),
	Webhook: z.url().regex(WebhookRegex).optional(),
});

const envData = {
	Devs: process.env.DEVS,
	DiscordClientId: process.env.DISCORD_CLIENT_ID,
	DiscordToken: process.env.DISCORD_TOKEN,
	NodeEnv: process.env.NODE_ENV,
	Postgres: process.env.POSTGRES,
	Proxy: process.env.PROXY,
	Redis: process.env.REDIS,
	Webhook: process.env.WEBHOOK,
	DockSock: process.env.DOCK_SOCK,
} as const;

export type Environment = z.infer<typeof envSchema>;

export const Env: Environment = await envSchema.parseAsync(envData).catch((error) => {
	console.error(z.prettifyError(error));
	process.exit(1);
});
