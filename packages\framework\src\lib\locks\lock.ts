import { randomUUID } from "node:crypto";
import { readFileSync } from "node:fs";
import { resolve } from "node:path";
import process from "node:process";
import { setTimeout as delay } from "node:timers/promises";
import type { Redis, Result } from "ioredis";
import { LockAcquisitionError, LockReleaseError, LockExtendError } from "./errors.js";

declare module "ioredis" {
	interface RedisCommander<Context> {
		delifequal(key: string, id: string): Result<string, Context>;
		pexpireifequal(key: string, id: string, seconds: number): Result<string, Context>;
	}
}

export interface Config {
	delay: number;
	jitter: number;
	retries: number;
	timeout: number;
}

export class Lock {
	public static readonly acquired = new Set<Lock>();

	public readonly id = randomUUID();

	public readonly config: Config;

	#key: string | null = null;

	#locked = false;

	public constructor(
		private readonly redisClient: Redis,
		options: Partial<Config> = {},
	) {
		if (!this.redisClient.delifequal) {
			this.redisClient.defineCommand("delifequal", {
				numberOfKeys: 1,
				// eslint-disable-next-line n/no-sync
				lua: readFileSync(resolve(import.meta.dirname, "..", "scripts", "delifequal.lua"), "utf8"),
			});
		}

		if (!this.redisClient.pexpireifequal) {
			this.redisClient.defineCommand("pexpireifequal", {
				numberOfKeys: 1,
				// eslint-disable-next-line n/no-sync
				lua: readFileSync(resolve(import.meta.dirname, "..", "scripts", "pexpireifequal.lua"), "utf8"),
			});
		}

		this.config = {
			timeout: 10_000,
			retries: 6,
			delay: 50,
			jitter: 1.2,
			...options,
		};

		if (this.config.jitter < 1) {
			process.emitWarning("jitter must be >= 1", "IoredisLock", "WARN001");
			this.config.jitter = 1;
		}
	}

	/**
	 * Acquire a lock for a key
	 */
	public async acquire(key: string): Promise<this> {
		this.#assertNotLocked();
		await this.#attempt(key, this.config.retries);
		this.#locked = true;
		this.#key = key;
		Lock.acquired.add(this);
		return this;
	}

	/**
	 * Extend lock TTL
	 */
	public async extend(ms = this.config.timeout): Promise<this> {
		this.#assertLocked();
		const ok = await this.redisClient.pexpireifequal(this.#key!, this.id, ms);
		if (!ok) {
			this.#unlockAndThrow(LockExtendError, `Lock on "${this.#key}" expired`);
		}

		return this;
	}

	/**
	 * Release lock
	 */
	public async release(): Promise<this> {
		this.#assertLocked();
		const key = this.#key!;
		const ok = await this.redisClient.delifequal(key, this.id);
		this.#unlock();
		if (!ok) {
			throw new LockReleaseError(`Lock on "${key}" expired`);
		}

		return this;
	}

	async #attempt(key: string, retries: number): Promise<void> {
		const ok = await this.redisClient.set(key, this.id, "PX", this.config.timeout, "NX");
		if (ok) {
			return;
		}

		if (retries <= 0) {
			throw new LockAcquisitionError(`Could not acquire lock on "${key}"`);
		}

		await delay(this.config.delay * (Math.random() * (this.config.jitter - 1) + 1));
		return this.#attempt(key, retries - 1);
	}

	#assertLocked(): void {
		if (!this.#locked || !this.#key) {
			throw new LockReleaseError("Lock not acquired");
		}
	}

	#assertNotLocked(): void {
		if (this.#locked) {
			throw new LockAcquisitionError("Lock already held");
		}
	}

	#unlock(): void {
		this.#locked = false;
		this.#key = null;
		Lock.acquired.delete(this);
	}

	#unlockAndThrow(ErrorClass: new (msg: string) => Error, msg: string): never {
		this.#unlock();
		throw new ErrorClass(msg);
	}

	public async [Symbol.asyncDispose]() {
		if (this.#locked && this.#key) {
			try {
				await this.release();
			} catch {}
		}
	}
}
