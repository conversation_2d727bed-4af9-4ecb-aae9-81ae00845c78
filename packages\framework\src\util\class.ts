import { ApplicationCommandType, ComponentType, type APIInteraction, InteractionType } from "discord-api-types/v10";
import {
	ButtonInteraction,
	ChatInputInteraction,
	BaseInteraction,
	AutoCompleteInteraction,
	ComponentInteraction,
} from "../structures/index.js";

export function resolveInteractionClass(data: APIInteraction) {
	const appCommands: Partial<Record<ApplicationCommandType, typeof ChatInputInteraction>> = {
		[ApplicationCommandType.ChatInput]: ChatInputInteraction,
	};

	const components: Partial<Record<ComponentType, typeof ButtonInteraction>> = {
		[ComponentType.Button]: ButtonInteraction,
	};

	// eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check
	switch (data.type) {
		case InteractionType.ApplicationCommand: {
			const type = data.data?.type;
			return appCommands[type] ?? BaseInteraction;
		}

		case InteractionType.MessageComponent: {
			const type = data.data?.component_type;
			return components[type] ?? ComponentInteraction;
		}

		case InteractionType.ApplicationCommandAutocomplete:
			return AutoCompleteInteraction;

		default:
			return BaseInteraction;
	}
}
