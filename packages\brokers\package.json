{"$schema": "https://json.schemastore.org/package.json", "name": "@fae/brokers", "version": "1.0.0", "description": "Powerful set of message brokers", "private": "true", "type": "module", "scripts": {"build": "tsup", "test": "vitest run --config ./vitest.config.ts", "lint": "biome check . && cross-env TIMING=1 eslint src --format=pretty", "format": "biome check . --write && cross-env TIMING=1 eslint src --fix --format=pretty", "prepack": "pnpm build"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "module": "./dist/index.js", "types": "./dist/index.d.ts", "directories": {"lib": "src"}, "files": ["dist", "scripts"], "dependencies": {"@msgpack/msgpack": "^3.1.2", "@vladfrangu/async_event_emitter": "^2.4.6", "ioredis": "^5.7.0", "kafkajs": "^2.2.4"}, "devDependencies": {"@types/node": "^24.3.0", "cross-env": "^10.0.0", "eslint": "^9.34.0", "eslint-config-neon": "^0.2.7", "eslint-formatter-compact": "^8.40.0", "eslint-formatter-pretty": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.9.2"}, "packageManager": "pnpm@10.15.0"}