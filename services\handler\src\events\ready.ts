import { GatewayDispatchEvents } from "@discordjs/core";
import { type Client, kClient, type Event } from "@fae/framework";
import { ActivityType, PresenceUpdateStatus } from "discord-api-types/v10";
import { inject, injectable } from "inversify";

@injectable()
export default class implements Event {
	public readonly name = "Client ready handling";

	public readonly event = GatewayDispatchEvents.Ready as const;

	public constructor(@inject(kClient) private readonly client: Client) {}

	public async execute(): Promise<void> {
		this.client.on(this.event, async () => {
			await this.client.updatePresence(0, {
				activities: [
					{
						name: "working from another world",
						type: ActivityType.Custom,
						state: "working from another world",
					},
				],
				status: PresenceUpdateStatus.Online,
				afk: false,
				since: null,
			});
		});
	}
}
