# Packages
node_modules
**/node_modules
package-lock.json

# Build artifacts
**/dist
typings
**/typings
tsconfig.tsbuildinfo

# Log files
logs
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed

# Docker (experimental)
docker
deploy
Dockerfile
.dockerignore
docker-compose.yml
docker-compose.dev.yml
docker-compose.prod.yml

# IDE
.vscode

# Tests
coverage
jest-setup.ts
jest.config.js

# Linting
.eslintignore
.eslintrc.json
tsconfig.eslint.json

# Miscellaneous
.tmp
.github
.git
.gitattributes
.gitignore
.npmrc
README.md
