{"$schema": "https://swc.rs/schema.json", "jsc": {"parser": {"syntax": "typescript", "tsx": false, "decorators": true, "dynamicImports": true}, "transform": {"legacyDecorator": true, "decoratorMetadata": true, "treatConstEnumAsEnum": true, "useDefineForClassFields": true, "verbatimModuleSyntax": true}, "baseUrl": "./", "paths": {"@/*": ["./src/*"]}, "target": "esnext", "keepClassNames": true, "preserveAllComments": false, "externalHelpers": true}, "module": {"type": "nodenext"}, "sourceMaps": true, "isModule": true}