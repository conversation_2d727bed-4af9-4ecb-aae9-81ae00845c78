import { PubSubRedisBroker, type RedisBrokerOptions } from "@fae/brokers";
import { encode, decode } from "../util/serialize.js";
import { createRedis } from "./createRedis.js";
import { container, kBroker } from "./inversify.js";

export function createBroker<T extends Record<string, unknown>>({
	group = "handler",
	name,
	encode: customEncode = encode,
	decode: customDecode = decode,
}: Partial<RedisBrokerOptions> = {}): PubSubRedisBroker<T> {
	const redis = createRedis();
	const broker = new PubSubRedisBroker<T>(redis, {
		group,
		name,
		encode: customEncode,
		decode: customDecode,
	});

	container.bind<PubSubRedisBroker<T>>(kBroker).toConstantValue(broker);
	return broker;
}
