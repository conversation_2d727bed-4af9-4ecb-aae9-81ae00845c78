# syntax=docker.io/docker/dockerfile:1

# ================ #
#    Base Stage    #
# ================ #
FROM node:alpine AS base

ENV CI=true
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

WORKDIR /fae

RUN corepack enable && corepack prepare pnpm@latest --activate

RUN apk update && apk add --no-cache gcompat dumb-init g++ make python3 

COPY . ./

# ================ #
#  Builder Stage   #
# ================ #
FROM base AS builder

ENV NODE_ENV="dev"
ENV TURBO_TELEMETRY_DISABLED=1
ENV DO_NOT_TRACK=1

RUN pnpm install --frozen-lockfile

RUN --mount=type=secret,id=TURBO_TEAM,env=TURBO_TEAM \
    --mount=type=secret,id=TURBO_TOKEN,env=TURBO_TOKEN \
    pnpm exec turbo run build --filter='@fae/handler...'

# ================ #
#  Pruned Stage    #
# ================ #
FROM builder AS pruned

RUN pnpm --filter='@fae/handler' --prod deploy pruned 

# ================ #
#  Runner Stage   #
# ================ #
FROM node:alpine

WORKDIR /fae

RUN addgroup --system --gid 996 docker \
 && addgroup --system --gid 1001 nodejs \
 && adduser --system --uid 1001 --ingroup nodejs fae \
 && adduser fae docker

USER fae

COPY --from=base /usr/bin/dumb-init /usr/bin/dumb-init
COPY --from=pruned /fae/pruned/dist /fae/dist
COPY --from=pruned /fae/pruned/node_modules /fae/node_modules
COPY --from=pruned /fae/pruned/package.json /fae/package.json

ENTRYPOINT ["dumb-init", "--"]

CMD ["node", "--run", "start"]
