import {
	ApplicationCommandOptionType,
	ApplicationCommandType,
	InteractionType,
	type APIApplicationCommandAutocompleteInteraction,
	type APIApplicationCommandInteraction,
	type APIApplicationCommandInteractionDataOption,
	type APIMessageApplicationCommandInteractionDataResolved,
	type APIInteractionDataResolved,
	type APIInteractionDataResolvedChannel,
	type APIInteractionDataResolvedGuildMember,
	type APIMessage,
	type APIModalSubmitInteraction,
	type APIRole,
	type APIUser,
	type APIUserInteractionDataResolved,
	type APIAttachment,
	type APIApplicationCommandInteractionDataIntegerOption,
	type APIApplicationCommandInteractionDataNumberOption,
	type APIApplicationCommandInteractionDataStringOption,
} from "discord-api-types/v10";

/**
 * Utility class for resolving command interaction options while working with the raw Discord API.
 *
 * This class provides type-safe accessors for the options and resolved data of
 * command and modal interactions, supporting subcommands and groups.
 *
 * Based on:
 * https://github.com/discordjs/discord.js/blob/main/packages/discord.js/src/structures/CommandInteractionOptionResolver.js
 */
export class InteractionOptionResolver {
	/**
	 * The underlying interaction object.
	 */
	private readonly interaction:
		| APIApplicationCommandAutocompleteInteraction
		| APIApplicationCommandInteraction
		| APIModalSubmitInteraction;

	/**
	 * The interaction's options array (may be null if no options provided).
	 */
	private readonly data: APIApplicationCommandInteractionDataOption[] | null = null;

	/**
	 * The resolved data attached to the interaction (users, roles, channels, etc.).
	 */
	private readonly resolved:
		| APIInteractionDataResolved
		| APIMessageApplicationCommandInteractionDataResolved
		| APIUserInteractionDataResolved
		| null = null;

	/**
	 * The options relevant to the bottom-level command or subcommand.
	 * If there is a subcommand group and/or subcommand, this will be their options.
	 */
	private readonly hoistedOptions: APIApplicationCommandInteractionDataOption[] | null = null;

	/**
	 * The name of the subcommand group, if any.
	 */
	private readonly group: string | null = null;

	/**
	 * The name of the subcommand, if any.
	 */
	private readonly subcommand: string | null = null;

	/**
	 * Creates a new InteractionOptionResolver instance.
	 *
	 * @param interaction - The interaction to resolve options for.
	 */
	public constructor(
		interaction:
			| APIApplicationCommandAutocompleteInteraction
			| APIApplicationCommandInteraction
			| APIModalSubmitInteraction,
	) {
		this.interaction = interaction;

		this.data = "options" in interaction.data ? (interaction.data.options ?? null) : null;

		this.resolved = "resolved" in interaction.data ? (interaction.data.resolved ?? null) : null;

		this.hoistedOptions = this.data;

		// Hoist subcommand group if present
		if (this.hoistedOptions?.[0]?.type === ApplicationCommandOptionType.SubcommandGroup) {
			this.group = this.hoistedOptions[0].name;
			this.hoistedOptions = this.hoistedOptions[0].options ?? [];
		}

		// Hoist subcommand if present
		if (this.hoistedOptions?.[0]?.type === ApplicationCommandOptionType.Subcommand) {
			this.subcommand = this.hoistedOptions[0].name;
			this.hoistedOptions = this.hoistedOptions[0].options ?? [];
		}
	}

	/**
	 * Gets an option by its name.
	 *
	 * @param name - The name of the option to retrieve.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The option if found, otherwise `null` or throws if `required` is `true`.
	 * @throws If the option is required but not found.
	 */
	public get<Required extends boolean = false>(
		name: string,
		required?: Required,
	): RequiredIf<Required, APIApplicationCommandInteractionDataOption>;

	public get(name: string, required = false): APIApplicationCommandInteractionDataOption | null {
		const option = this.hoistedOptions?.find((opt) => opt.name === name);
		if (!option) {
			if (required) {
				throw new Error(`Missing required option "${name}"`);
			}

			return null;
		}

		return option;
	}

	/**
	 * Gets the selected subcommand name.
	 *
	 * @param required - Whether to throw if no subcommand is selected.
	 * @returns The subcommand name or `null` if none selected.
	 * @throws If `required` is true and no subcommand is selected.
	 */
	public getSubcommand<Required extends boolean = false>(required?: Required): RequiredIf<Required, string>;

	public getSubcommand(required = true): string | null {
		if (required && !this.subcommand) {
			throw new Error("A subcommand was not selected");
		}

		return this.subcommand;
	}

	/**
	 * Gets the selected subcommand group name.
	 *
	 * @param required - Whether to throw if no subcommand group is selected.
	 * @returns The subcommand group name or `null` if none selected.
	 * @throws If `required` is true and no subcommand group is selected.
	 */
	public getSubcommandGroup<Required extends boolean = false>(required?: Required): RequiredIf<Required, string>;

	public getSubcommandGroup(required = true): string | null {
		if (required && !this.group) {
			throw new Error("A subcommand group was not selected");
		}

		return this.group;
	}

	/**
	 * Gets a boolean option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The boolean value or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getBoolean<Required extends boolean = false>(
		name: string,
		required?: Required,
	): RequiredIf<Required, boolean>;

	public getBoolean(name: string, required = false): boolean | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.Boolean, required);
		return option?.value ?? null;
	}

	/**
	 * Gets a channel option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The resolved channel or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getChannel<Required extends boolean = false>(
		name: string,
		required?: Required,
	): RequiredIf<Required, APIInteractionDataResolvedChannel>;

	public getChannel(name: string, required = false): APIInteractionDataResolvedChannel | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.Channel, required);
		return option && this.resolved && "channels" in this.resolved
			? (this.resolved.channels?.[option.value] ?? null)
			: null;
	}

	/**
	 * Gets a string option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The string value or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getString<Required extends boolean = false>(name: string, required?: Required): RequiredIf<Required, string>;

	public getString(name: string, required = false): string | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.String, required);
		return option?.value ?? null;
	}

	/**
	 * Gets an integer option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The integer value or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getInteger<Required extends boolean = false>(
		name: string,
		required?: Required,
	): RequiredIf<Required, number>;

	public getInteger(name: string, required = false): number | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.Integer, required);
		return (option?.value as number | null) ?? null;
	}

	/**
	 * Gets a number option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The number value or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getNumber<Required extends boolean = false>(name: string, required?: Required): RequiredIf<Required, number>;

	public getNumber(name: string, required = false): number | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.Number, required);
		return (option?.value as number | null) ?? null;
	}

	/**
	 * Gets a user option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The resolved user or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getUser<Required extends boolean = false>(name: string, required?: Required): RequiredIf<Required, APIUser>;

	public getUser(name: string, required = false): APIUser | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.User, required);
		return option && this.resolved && "users" in this.resolved
			? (this.resolved.users?.[option.value] ?? null)
			: null;
	}

	/**
	 * Gets a member option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The resolved guild member or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getMember<Required extends boolean = false>(
		name: string,
		required?: Required,
	): RequiredIf<Required, APIInteractionDataResolvedGuildMember>;

	public getMember(name: string, required = false): APIInteractionDataResolvedGuildMember | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.User, required);
		return option && this.resolved && "members" in this.resolved
			? (this.resolved.members?.[option.value] ?? null)
			: null;
	}

	/**
	 * Gets a role option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The resolved role or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getRole<Required extends boolean = false>(name: string, required?: Required): RequiredIf<Required, APIRole>;

	public getRole(name: string, required = false): APIRole | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.Role, required);
		return option && this.resolved && "roles" in this.resolved
			? (this.resolved.roles?.[option.value] ?? null)
			: null;
	}

	/**
	 * Gets an attachment option value.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The resolved attachment or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getAttachment<Required extends boolean = false>(
		name: string,
		required?: Required,
	): RequiredIf<Required, APIAttachment>;

	public getAttachment(name: string, required = false): APIAttachment | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.Attachment, required);
		return option && this.resolved && "attachments" in this.resolved
			? (this.resolved.attachments?.[option.value] ?? null)
			: null;
	}

	/**
	 * Gets a mentionable option value.
	 *
	 * This can be a user, role, or guild member.
	 *
	 * @param name - The option name.
	 * @param required - Whether to throw if the option is not found.
	 * @returns The resolved mentionable or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	public getMentionable<Required extends boolean = false>(
		name: string,
		required?: Required,
	): RequiredIf<Required, APIInteractionDataResolvedGuildMember | APIRole | APIUser>;

	public getMentionable(
		name: string,
		required = false,
	): APIInteractionDataResolvedGuildMember | APIRole | APIUser | null {
		const option = this.getTypedOption(name, ApplicationCommandOptionType.Mentionable, required);

		if (!option || !this.resolved) {
			return null;
		}

		if ("members" in this.resolved && this.resolved.members && option.value in this.resolved.members) {
			return this.resolved.members[option.value] ?? null;
		}

		if ("users" in this.resolved && this.resolved.users && option.value in this.resolved.users) {
			return this.resolved.users[option.value] ?? null;
		}

		if ("roles" in this.resolved && this.resolved.roles && option.value in this.resolved.roles) {
			return this.resolved.roles[option.value] ?? null;
		}

		return null;
	}

	/**
	 * Gets the target user for a user context menu interaction.
	 *
	 * @throws If used on a non-user context menu interaction.
	 */
	public getTargetUser(): APIUser {
		if (
			this.interaction.type !== InteractionType.ApplicationCommand ||
			this.interaction.data.type !== ApplicationCommandType.User
		) {
			throw new Error("This method can only be used on user context menu interactions");
		}

		return (this.resolved as APIUserInteractionDataResolved).users[this.interaction.data.target_id] as APIUser;
	}

	/**
	 * Gets the target member for a user context menu interaction.
	 *
	 * @param required - Whether to throw if member data is missing.
	 * @returns The resolved member or `null`.
	 * @throws If used on a non-user context menu interaction or if required and member data is missing.
	 */
	public getTargetMember<Required extends boolean = false>(
		required?: Required,
	): RequiredIf<Required, APIInteractionDataResolvedGuildMember>;

	public getTargetMember(required = false): APIInteractionDataResolvedGuildMember | null {
		if (
			this.interaction.type !== InteractionType.ApplicationCommand ||
			this.interaction.data.type !== ApplicationCommandType.User
		) {
			throw new Error("This method can only be used on user context menu interactions");
		}

		const member =
			(this.resolved as APIUserInteractionDataResolved).members?.[this.interaction.data.target_id] ?? null;

		if (!member && required) {
			throw new Error("Member data is not present");
		}

		return member;
	}

	/**
	 * Gets the target message for a message context menu interaction.
	 *
	 * @throws If used on a non-message context menu interaction.
	 */
	public getTargetMessage(): APIMessage {
		if (
			this.interaction.type !== InteractionType.ApplicationCommand ||
			this.interaction.data.type !== ApplicationCommandType.Message
		) {
			throw new Error("This method can only be used on message context menu interactions");
		}

		return (this.resolved as APIMessageApplicationCommandInteractionDataResolved).messages[
			this.interaction.data.target_id
		] as APIMessage;
	}

	/**
	 * Gets the focused option for an autocomplete interaction.
	 *
	 * @throws If used on a non-autocomplete interaction or if no option is focused.
	 */
	public getFocusedOption():
		| APIApplicationCommandInteractionDataIntegerOption
		| APIApplicationCommandInteractionDataNumberOption
		| APIApplicationCommandInteractionDataStringOption {
		if (this.interaction.type !== InteractionType.ApplicationCommandAutocomplete) {
			throw new Error("This method can only be used on autocomplete interactions");
		}

		const focusedOption = this.hoistedOptions?.find((option) => "focused" in option && option.focused) as
			| APIApplicationCommandInteractionDataIntegerOption
			| APIApplicationCommandInteractionDataNumberOption
			| APIApplicationCommandInteractionDataStringOption
			| undefined;

		if (!focusedOption) {
			throw new Error("No focused option for autocomplete interaction");
		}

		const { focused, ...option } = focusedOption;
		return option;
	}

	/**
	 * Internal helper to get an option by name and validate its type.
	 *
	 * @param name - The option name.
	 * @param type - The expected option type.
	 * @param required - Whether to throw if the option is missing or of wrong type.
	 * @returns The option of the expected type or `null`.
	 * @throws If the option is required but missing or the wrong type.
	 */
	private getTypedOption<Option extends BasicApplicationCommandOptionType, Required extends boolean = false>(
		name: string,
		type: Option,
		required: Required,
	): RequiredIf<Required, TypeToOptionMap[Option]>;

	private getTypedOption<Option extends BasicApplicationCommandOptionType>(
		name: string,
		type: Option,
		required: boolean,
	): TypeToOptionMap[Option] | null {
		const option = this.get(name, required);
		if (!option) {
			return null;
		}

		if (option.type !== type) {
			throw new Error(`Option with name "${name}" is not of the correct type`);
		}

		return option as TypeToOptionMap[Option];
	}
}

/**
 * The subset of basic command option types used internally.
 */
type BasicApplicationCommandOptionType = APIApplicationCommandInteractionDataOption["type"];

/**
 * Internal mapping from option type to corresponding option interface.
 */
type _TypeToOptionMap = {
	[Option in BasicApplicationCommandOptionType]: APIApplicationCommandInteractionDataOption & { type: Option };
};

/**
 * Public mapping from option type to corresponding option interface.
 */
type TypeToOptionMap = {
	[Option in keyof _TypeToOptionMap]: _TypeToOptionMap[Option];
};

/**
 * Conditional type to choose between two types based on a boolean.
 */
type If<Value extends boolean, TrueResult, FalseResult> = Value extends true
	? TrueResult
	: Value extends false
		? FalseResult
		: FalseResult | TrueResult;

/**
 * Returns `ValueType` if `Value` is `true`, otherwise `FallbackType | ValueType`.
 */
type RequiredIf<Value extends boolean, ValueType, FallbackType = null> = If<Value, ValueType, FallbackType | ValueType>;
