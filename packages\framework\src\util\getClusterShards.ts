/**
 * Returns the inclusive shard range owned by a cluster.
 *
 * @param cluster - Zero-based cluster index.
 * @param totalShards - Total number of shards.
 * @param shardCount - Number of shards per cluster.
 * @returns A tuple `[start, end]` of shard IDs for the cluster.
 * @example
 * ```ts
 * getClusterShards(8, 560, 16);
 * // → [128, 143]
 * ```
 * @remarks
 * - Cluster indices are zero-based.
 * - The end value is capped at `totalShards - 1`.
 * - If shards don’t divide evenly, compute `shardCount` as
 *   `Math.ceil(totalShards / clusterCount)`.
 */
export function getClusterShards(cluster: number, totalShards: number, shardCount: number) {
	const start = cluster * shardCount;
	return [start, Math.min(start + shardCount - 1, totalShards - 1)];
}
