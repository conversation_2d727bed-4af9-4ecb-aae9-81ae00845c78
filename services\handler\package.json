{"name": "@fae/handler", "version": "1.0.0", "main": "dist/src/index.js", "type": "module", "scripts": {"build:clean": "del-cli dist", "build:check": "tsc --noEmit", "build:esm": "swc ./src --strip-leading-paths --out-dir ./dist", "build": "pnpm build:clean && pnpm build:check && pnpm build:esm", "lint": "biome check . && cross-env TIMING=1 eslint src --format=pretty", "format": "biome check . --write && cross-env TIMING=1 eslint src --fix --format=pretty", "start": "node --enable-source-maps dist/index.js", "deploy:commands": "node --enable-source-maps dist/deploy.js"}, "dependencies": {"@discordjs/collection": "dev", "@discordjs/core": "3.0.0-dev.1754524906-698b92c2a", "@discordjs/formatters": "dev", "@discordjs/util": "1.1.1", "@fae/brokers": "workspace:*", "@fae/framework": "workspace:^", "@sapphire/snowflake": "3.5.5", "@swc/helpers": "^0.5.17", "@vladfrangu/async_event_emitter": "^2.4.6", "discord-api-types": "0.38.22", "inversify": "7.9.1", "ioredis": "5.7.0", "pino": "9.9.0", "reflect-metadata": "^0.2.2", "tinyglobby": "^0.2.14"}, "devDependencies": {"@swc/cli": "^0.7.8", "@swc/core": "^1.13.5", "@types/node": "^24.3.0", "cross-env": "^10.0.0", "del-cli": "^6.0.0", "eslint": "9.34.0", "eslint-config-neon": "0.2.7", "eslint-formatter-compact": "8.40.0", "eslint-formatter-pretty": "6.0.1", "typescript": "5.9.2"}, "packageManager": "pnpm@10.15.0"}