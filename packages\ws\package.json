{"$schema": "https://json.schemastore.org/package.json", "name": "@fae/ws", "version": "2.0.5", "description": "Wrapper around <PERSON><PERSON>'s gateway", "type": "module", "scripts": {"build": "tsup", "test": "vitest run --config ./vitest.config.ts", "lint": "biome check . && cross-env TIMING=1 eslint src --format=pretty", "format": "biome check . --write && cross-env TIMING=1 eslint src --fix --format=pretty", "prepack": "pnpm build"}, "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "directories": {"lib": "src"}, "files": ["dist"], "dependencies": {"@discordjs/collection": "^2.1.1", "@discordjs/rest": "^2.6.0", "@discordjs/util": "^1.1.1", "@sapphire/async-queue": "^1.5.5", "@vladfrangu/async_event_emitter": "^2.4.6", "discord-api-types": "^0.38.22", "ws": "^8.18.3"}, "devDependencies": {"@types/node": "^24.3.0", "@types/ws": "^8.18.1", "cross-env": "^10.0.0", "eslint": "9.34.0", "eslint-config-neon": "0.2.7", "eslint-formatter-compact": "8.40.0", "eslint-formatter-pretty": "6.0.1", "tsup": "8.5.0", "typescript": "5.9.2"}, "packageManager": "pnpm@10.15.0"}