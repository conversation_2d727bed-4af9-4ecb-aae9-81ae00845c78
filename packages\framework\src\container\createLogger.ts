import pino, { type Logger } from "pino";
import { container, kLogger } from "./inversify.js";

export function createLogger(origin?: string) {
	const transports = pino.transport({
		targets: [
			{
				target: "pino/file",
				level: "info",
				options: { destination: 1, append: false },
			},
		],
	});

	const logger = pino(
		{
			base: undefined,
			name: origin ?? "handler",
			level: "info",
			timestamp: pino.stdTimeFunctions.isoTime,
			formatters: {
				level: (level) => ({ level }),
			},
		},
		transports,
	);

	container.bind<Logger>(kLogger).toConstantValue(logger);
	return logger;
}
